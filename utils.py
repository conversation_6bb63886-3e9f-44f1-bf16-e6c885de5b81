from fastapi import Request, UploadFile
from sqlalchemy.orm import Session
from models import UserDB
from jose import jwt
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
import secrets
import logging
import os

# Setup logger
logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, "your-secret-key", algorithm="HS256")
    return encoded_jwt

def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, "your-secret-key", algorithms=["HS256"])
        username: str = payload.get("sub")
        if username is None:
            raise Exception("Invalid token")
        return username
    except jwt.JWTError:
        raise Exception("Could not validate credentials")

def get_current_user(request: Request, db: Session):
    auth_header = request.cookies.get("access_token", "")
    print("Access Token:", auth_header)  # Debug
    if not auth_header.startswith("Bearer "):
        return None
    token = auth_header[len("Bearer "):]
    print("Token:", token)  # Debug
    username = decode_access_token(token)
    if not username:
        return None
    return db.query(UserDB).filter(UserDB.username == username).first()

POST_IMAGES_DIR = "static/posts"
def save_post_image(image: UploadFile, username: str) -> str:
    """
    Save uploaded post image and return its URL path
    """
    if not os.path.exists(POST_IMAGES_DIR):
        os.makedirs(POST_IMAGES_DIR)

    # Create safe filename
    filename = f"{username}_{image.filename}"
    file_path = os.path.join(POST_IMAGES_DIR, filename)

    # Write file to disk
    with open(file_path, "wb") as buffer:
        buffer.write(image.file.read())

    return f"/{POST_IMAGES_DIR}/{filename}"

def catch_phase(content: str):
    """
    Checks if message/post contains restricted words.
    Returns the first match, or None if clean.
    """
    restricted_words = {
        'spam', 'scam', 'fraud', 'phishing', 'hate', 'racist', 'sexist'
    }
    


    for word in restricted_words:
        if word in content:
            return word  # Return matched restricted word
    return None  # No restricted words found
    

def save_post_image(image: UploadFile, username: str):
    filename = f"{username}_{image.filename}"
    path = f"static/images/{filename}"
    with open(path, "wb") as f:
        f.write(image.file.read())
    return f"/static/images/{filename}"

def get_context(page_type: str, user=None):
    contexts = {
        "index": {"type": "index", "description": "Welcome to RealHonest – Honest conversations for visionary minds"},
        "dashboard": {"type": "dashboard", "description": "Your personal space to share insights and connect"},
        "profile": {"type": "profile", "description": "Manage your profile and preferences"},
        "create_post": {"type": "post", "description": "Share something meaningful with the community"}
    }
    base = contexts.get(page_type, {"type": "default"})
    if user:
        base["user"] = {
            "username": user.username,
            "role": user.role,
            "full_name": user.full_name,
            "avatar_url": user.avatar_url
        }
    return base

def get_cached_number_fact(number: int = None):
    if number in NUMBER_FACT_CACHE:
        return NUMBER_FACT_CACHE[number]
    if not number:
        number = "random"
    try:
        res = requests.get(f"http://numbersapi.com/{number}/trivia")
        fact = res.text
        NUMBER_FACT_CACHE[number] = fact
        return fact
    except Exception:
        return "There's always something new to learn."

    

    
