import os
import boto3
from botocore.exceptions import NoCredentialsError
from fastapi import UploadFile
import secrets
import shutil
import io
from PIL import Image  # ✅ Fixed typo: from PIL import Image (not 'image')

class MediaUploadManager:
    def __init__(self, base_dir="static"):
        self.base_dir = base_dir
        self.allowed_types = ["image/jpeg", "image/png", "image/webp"]
        self.max_size_mb = 5

    def _ensure_directory(self, path):
        """Ensure directory exists"""
        os.makedirs(path, exist_ok=True)

    def validate_image(self, file: UploadFile):
        """Validate file type and size"""
        if file.content_type not in self.allowed_types:
            raise ValueError(f"Unsupported image format: {file.content_type}. Supported types: {', '.join(self.allowed_types)}")

        # Reset file pointer and check size
        file.file.seek(0, os.SEEK_END)
        file_size = file.file.tell()
        file.file.seek(0)

        if file_size > self.max_size_mb * 1024 * 1024:
            raise ValueError(f"File too large. Max size is {self.max_size_mb}MB")
        
        return True

    def resize_and_compress(self, file: UploadFile, max_size=(800, 800)):
        """Resize and compress image before saving"""
        try:
            img = Image.open(io.BytesIO(file.file.read()))
            img = img.convert("RGB")  # Ensure RGB
            img.thumbnail(max_size)  # Resize while keeping aspect ratio

            # Save compressed image
            output = io.BytesIO()
            img.save(output, format=img.format, optimize=True, quality=85)
            output.seek(0)

            # Reset original file pointer for reuse
            file.file.seek(0)
            return output
        except Exception as e:
            raise ValueError(f"Invalid image file: {str(e)}")

    def save_avatar(self, username: str, file: UploadFile):
        """Save avatar image locally"""
        self.validate_image(file)
        resized_stream = self.resize_and_compress(file)

        filename = f"{username}_{secrets.token_hex(8)}_{file.filename}"
        folder = os.path.join(self.base_dir, "avatars")
        self._ensure_directory(folder)
        file_path = os.path.join(folder, filename)

        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(resized_stream, buffer)

        return f"/static/avatars/{filename}"

    def save_post_image(self, file: UploadFile):
        """Save post image locally"""
        self.validate_image(file)
        resized_stream = self.resize_and_compress(file)

        filename = f"post_{secrets.token_hex(8)}_{file.filename}"
        folder = os.path.join(self.base_dir, "posts")
        self._ensure_directory(folder)
        file_path = os.path.join(folder, filename)

        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(resized_stream, buffer)

        return f"/static/posts/{filename}"

    def delete_file(self, file_url: str):
        """Delete local file by URL path"""
        if not file_url or "/static/" not in file_url:
            return False
        try:
            relative_path = file_url.replace("/static/", "")
            full_path = os.path.join(self.base_dir, relative_path)
            if os.path.exists(full_path):
                os.remove(full_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False


class S3Manager:
    def __init__(self, bucket_name):
        self.s3_client = boto3.client('s3')
        self.bucket_name = bucket_name

    def upload_file(self, file, key):
        """Upload file to AWS S3 bucket"""
        try:
            self.s3_client.upload_fileobj(
                file.file,
                self.bucket_name,
                key,
                ExtraArgs={'ContentType': file.content_type}
            )
            return f"https://{self.bucket_name}.s3.amazonaws.com/{key}"
        except NoCredentialsError:
            raise Exception("AWS credentials not available")
        except Exception as e:
            raise Exception(f"S3 upload failed: {str(e)}")


def process_and_upload(file: UploadFile, manager_type="local", s3_bucket=None):
    """
    Unified function to handle both local and cloud uploads
    Example usage:
        url = process_and_upload(image, manager_type="s3", s3_bucket="realhonest-media")
    """
    if manager_type == "s3":
        if not s3_bucket:
            raise ValueError("S3 bucket name required for cloud storage")
        s3 = S3Manager(bucket_name=s3_bucket)
        return s3.upload_file(file, f"posts/post_{secrets.token_hex(8)}_{file.filename}")
    else:
        local = MediaUploadManager()
        return local.save_post_image(file)