from .custom import translations as custom_translations


"""
bg locale file.

It has been generated automatically and must not be modified directly.
"""


locale = {
    'plural': lambda n: 'one' if (n == n and ((n == 1))) else 'other',
    'ordinal': lambda n: 'other',
    'translations': {
        'days': {
            'abbreviated': {
                0: 'пн',
                1: 'вт',
                2: 'ср',
                3: 'чт',
                4: 'пт',
                5: 'сб',
                6: 'нд',
            },
            'narrow': {
                0: 'п',
                1: 'в',
                2: 'с',
                3: 'ч',
                4: 'п',
                5: 'с',
                6: 'н',
            },
            'short': {
                0: 'пн',
                1: 'вт',
                2: 'ср',
                3: 'чт',
                4: 'пт',
                5: 'сб',
                6: 'нд',
            },
            'wide': {
                0: 'понеделник',
                1: 'вторник',
                2: 'сряда',
                3: 'четвъртък',
                4: 'петък',
                5: 'събота',
                6: 'неделя',
            },
        },
        'months': {
            'abbreviated': {
                1: 'яну',
                2: 'фев',
                3: 'март',
                4: 'апр',
                5: 'май',
                6: 'юни',
                7: 'юли',
                8: 'авг',
                9: 'сеп',
                10: 'окт',
                11: 'ное',
                12: 'дек',
            },
            'narrow': {
                1: 'я',
                2: 'ф',
                3: 'м',
                4: 'а',
                5: 'м',
                6: 'ю',
                7: 'ю',
                8: 'а',
                9: 'с',
                10: 'о',
                11: 'н',
                12: 'д',
            },
            'wide': {
                1: 'януари',
                2: 'февруари',
                3: 'март',
                4: 'април',
                5: 'май',
                6: 'юни',
                7: 'юли',
                8: 'август',
                9: 'септември',
                10: 'октомври',
                11: 'ноември',
                12: 'декември',
            },
        },
        'units': {
            'year': {
                'one': '{0} година',
                'other': '{0} години',
            },
            'month': {
                'one': '{0} месец',
                'other': '{0} месеца',
            },
            'week': {
                'one': '{0} седмица',
                'other': '{0} седмици',
            },
            'day': {
                'one': '{0} ден',
                'other': '{0} дни',
            },
            'hour': {
                'one': '{0} час',
                'other': '{0} часа',
            },
            'minute': {
                'one': '{0} минута',
                'other': '{0} минути',
            },
            'second': {
                'one': '{0} секунда',
                'other': '{0} секунди',
            },
            'microsecond': {
                'one': '{0} микросекунда',
                'other': '{0} микросекунди',
            },
        },
        'relative': {
            'year': {
                'future': {
                    'other': 'след {0} години',
                    'one': 'след {0} година',
                },
                'past': {
                    'other': 'преди {0} години',
                    'one': 'преди {0} година',
                },
            },
            'month': {
                'future': {
                    'other': 'след {0} месеца',
                    'one': 'след {0} месец',
                },
                'past': {
                    'other': 'преди {0} месеца',
                    'one': 'преди {0} месец',
                },
            },
            'week': {
                'future': {
                    'other': 'след {0} седмици',
                    'one': 'след {0} седмица',
                },
                'past': {
                    'other': 'преди {0} седмици',
                    'one': 'преди {0} седмица',
                },
            },
            'day': {
                'future': {
                    'other': 'след {0} дни',
                    'one': 'след {0} ден',
                },
                'past': {
                    'other': 'преди {0} дни',
                    'one': 'преди {0} ден',
                },
            },
            'hour': {
                'future': {
                    'other': 'след {0} часа',
                    'one': 'след {0} час',
                },
                'past': {
                    'other': 'преди {0} часа',
                    'one': 'преди {0} час',
                },
            },
            'minute': {
                'future': {
                    'other': 'след {0} минути',
                    'one': 'след {0} минута',
                },
                'past': {
                    'other': 'преди {0} минути',
                    'one': 'преди {0} минута',
                },
            },
            'second': {
                'future': {
                    'other': 'след {0} секунди',
                    'one': 'след {0} секунда',
                },
                'past': {
                    'other': 'преди {0} секунди',
                    'one': 'преди {0} секунда',
                },
            },
        },
        'day_periods': {
            'midnight': 'полунощ',
            'am': 'пр.об.',
            'pm': 'сл.об.',
            'morning1': 'сутринта',
            'morning2': 'на обяд',
            'afternoon1': 'следобед',
            'evening1': 'вечерта',
            'night1': 'през нощта',
        },
        'week_data': {
            'min_days': 1,
            'first_day': 0,
            'weekend_start': 5,
            'weekend_end': 6,
        },
    },
    'custom': custom_translations
}
