from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Enum as SaEnum
from sqlalchemy.orm import relationship, declarative_base
from database import Base
import enum
import datetime
from datetime import datetime  # <-- Add this line to import the datetime class
from enum import Enum as PyEnum

Base = declarative_base()

class MentorCategory(PyEnum):
    RESEARCH = "Research"
    ENTREPRENEURSHIP = "Entrepreneurship"
    EDUCATION = "Education"
    SOCIAL_IMPACT = "Social Impact"

class MentorDB(Base):
    __tablename__ = "mentors"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    full_name = Column(String(100))
    category = Column(SaEnum(MentorCategory))
    expertise = Column(String(255))
    bio = Column(Text)
    qualifications = Column(Text)
    experience_years = Column(Integer)
    hourly_rate = Column(String(50))  # Or Float if you accept real money values
    available = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)  # now works

    user = relationship("UserDB", back_populates="mentor_profile")

class UserDB(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100))
    role = Column(String(20), default="author")
    full_name = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(200), default="/static/avatars/default.png")
    verification_token = Column(String(100), nullable=True)
    email_verified = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    

    github_username = Column(String(100), nullable=True)
    
    posts = relationship("PostDB", back_populates="author")
    mentor_profile = relationship("MentorDB", uselist=False, back_populates="user")

class PostDB(Base):
    __tablename__ = "posts"
    id = Column(Integer, primary_key=True)
    title = Column(String(200))
    content = Column(Text)
    image_url = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    author_id = Column(Integer, ForeignKey("users.id"))

    author = relationship("UserDB", back_populates="posts")

class ChatMessageDB(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True)
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    receiver_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # null means group/public chat
    text = Column(Text, nullable=False)
    is_direct = Column(Boolean, default=False)  # True if private DM
    read = Column(Boolean, default=False)     # For message status tracking
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    sender = relationship("UserDB", foreign_keys=[sender_id])
    receiver = relationship("UserDB", foreign_keys=[receiver_id])