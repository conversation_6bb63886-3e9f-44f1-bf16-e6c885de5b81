total 168
drwxrwxr-x 13 <USER> <GROUP>  4096 Jun 21 10:51 [0m[01;34m.[0m
drwxr-x--- 45 <USER> <GROUP>  4096 Jun 21 10:29 [01;34m..[0m
drwxrwxr-x  4 <USER> <GROUP>  4096 Jun 12 13:05 [01;34malembic[0m
-rw-rw-r--  1 <USER> <GROUP>  3591 Jun 12 13:19 alembic.ini
-rw-rw-r--  1 <USER> <GROUP>   395 Jun  8 14:41 check_db.py
-rw-rw-r--  1 <USER> <GROUP>   614 Jun 20 12:20 database.py
drwxrwxr-x  3 <USER> <GROUP>  4096 Jun 12 15:13 [01;34m.docker[0m
-rw-rw-r--  1 <USER> <GROUP>   683 Jun 12 14:17 dockerfile
-rw-rw-r--  1 <USER> <GROUP>    93 Jun 12 14:18 .dockerignore
-rw-rw-r--  1 <USER> <GROUP>   190 Jun 13 13:16 .env
-rw-rw-r--  1 <USER> <GROUP>    74 Jun 16 14:33 input.css
-rw-rw-r--  1 <USER> <GROUP> 26116 Jun 20 14:29 main.py
-rw-rw-r--  1 <USER> <GROUP>  2968 Jun 19 15:50 models.py
drwxrwxr-x  3 <USER> <GROUP>  4096 Jun 14 14:22 [01;34mnode_modules[0m
-rw-rw-r--  1 <USER> <GROUP>   375 Jun 16 15:00 package.json
-rw-rw-r--  1 <USER> <GROUP>   565 Jun 16 15:01 package-lock.json
-rw-rw-r--  1 <USER> <GROUP>     0 Jun 21 10:53 project_structure.txt
drwxrwxr-x  2 <USER> <GROUP>  4096 Jun 20 14:29 [01;34m__pycache__[0m
drwxrwxr-x  2 <USER> <GROUP>  4096 Jun  4 17:49 [01;34m.qodo[0m
-rw-rw-r--  1 <USER> <GROUP>   504 Jun 12 16:09 README.md
-rw-r--r--  1 <USER> <GROUP> 28672 Jun 20 13:13 realhonest.db
-rw-rw-r--  1 <USER> <GROUP>     0 Jun 12 16:12 realhonestdb
-rw-rw-r--  1 <USER> <GROUP>   367 Jun 19 15:40 requirements.txt
drwxrwxr-x  2 <USER> <GROUP>  4096 Jun 16 11:57 [01;34mscripts[0m
drwxrwxr-x  5 <USER> <GROUP>  4096 Jun 19 12:01 [01;34mstatic[0m
-rw-rw-r--  1 <USER> <GROUP>   194 Jun 16 14:32 tailwind.config.js
drwxrwxr-x  3 <USER> <GROUP>  4096 Jun 19 15:51 [01;34mtemplates[0m
-rw-rw-r--  1 <USER> <GROUP>  1814 Jun 20 10:49 utils.py
drwxrwxr-x  5 <USER> <GROUP>  4096 Jun  4 18:58 [01;34m.venv[0m
drwxrwxr-x  5 <USER> <GROUP>  4096 Jun  4 19:29 [01;34mvenv[0m
drwxrwxr-x  2 <USER> <GROUP>  4096 Jun  4 18:55 [01;34m.vscode[0m
