{"version": 3, "names": ["transformFile", "exports", "filename", "opts", "callback", "Error", "transformFileSync", "transformFileAsync", "Promise", "reject"], "sources": ["../src/transform-file-browser.ts"], "sourcesContent": ["/* c8 ignore start */\n\n// duplicated from transform-file so we do not have to import anything here\ntype TransformFile = {\n  (filename: string, callback: (error: Error, file: null) => void): void;\n  (\n    filename: string,\n    opts: any,\n    callback: (error: Error, file: null) => void,\n  ): void;\n};\n\nexport const transformFile: TransformFile = function transformFile(\n  filename,\n  opts,\n  callback?: (error: Error, file: null) => void,\n) {\n  if (typeof opts === \"function\") {\n    callback = opts;\n  }\n\n  callback(new Error(\"Transforming files is not supported in browsers\"), null);\n};\n\nexport function transformFileSync(): never {\n  throw new Error(\"Transforming files is not supported in browsers\");\n}\n\nexport function transformFileAsync() {\n  return Promise.reject(\n    new Error(\"Transforming files is not supported in browsers\"),\n  );\n}\n"], "mappings": ";;;;;;;;AAYO,MAAMA,aAA4B,GAAAC,OAAA,CAAAD,aAAA,GAAG,SAASA,aAAaA,CAChEE,QAAQ,EACRC,IAAI,EACJC,QAA6C,EAC7C;EACA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC9BC,QAAQ,GAAGD,IAAI;EACjB;EAEAC,QAAQ,CAAC,IAAIC,KAAK,CAAC,iDAAiD,CAAC,EAAE,IAAI,CAAC;AAC9E,CAAC;AAEM,SAASC,iBAAiBA,CAAA,EAAU;EACzC,MAAM,IAAID,KAAK,CAAC,iDAAiD,CAAC;AACpE;AAEO,SAASE,kBAAkBA,CAAA,EAAG;EACnC,OAAOC,OAAO,CAACC,MAAM,CACnB,IAAIJ,KAAK,CAAC,iDAAiD,CAC7D,CAAC;AACH;AAAC", "ignoreList": []}