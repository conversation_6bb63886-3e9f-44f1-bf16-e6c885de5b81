{"version": 3, "file": "router.umd.min.js", "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface HandlerResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData\n  status?: number;\n}\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  unstable_actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => Promise<HandlerResult>\n  ) => Promise<HandlerResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<HandlerResult[]>;\n}\n\nexport interface AgnosticPatchRoutesOnMissFunction<\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> {\n  (opts: {\n    path: string;\n    matches: M[];\n    patch: (routeId: string | null, children: AgnosticRouteObject[]) => void;\n  }): void | Promise<void>;\n}\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nfunction decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  HandlerResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnMissFunction,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  unstable_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  unstable_patchRoutesOnMiss?: AgnosticPatchRoutesOnMissFunction;\n  unstable_dataStrategy?: DataStrategyFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      unstable_dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      unstable_dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      unstable_viewTransitionOpts?: ViewTransitionOpts;\n      unstable_flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  unstable_flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  unstable_viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.unstable_dataStrategy || defaultDataStrategy;\n  let patchRoutesOnMissImpl = init.unstable_patchRoutesOnMiss;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    unstable_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnMissImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // If the user provided a patchRoutesOnMiss implementation and our initial\n  // match is a splat route, clear them out so we run through lazy discovery\n  // on hydration in case there's a more accurate lazy route match\n  if (initialMatches && patchRoutesOnMissImpl) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    // We need to run patchRoutesOnMiss in initialize()\n    initialized = false;\n    initialMatches = [];\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    let isRouteInitialized = (m: AgnosticDataRouteMatch) => {\n      // No loader, nothing to initialize\n      if (!m.route.loader) {\n        return true;\n      }\n      // Explicitly opting-in to running on hydration\n      if (\n        typeof m.route.loader === \"function\" &&\n        m.route.loader.hydrate === true\n      ) {\n        return false;\n      }\n      // Otherwise, initialized if hydrated with data or an error\n      return (\n        (loaderData && loaderData[m.route.id] !== undefined) ||\n        (errors && errors[m.route.id] !== undefined)\n      );\n    };\n\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches.slice(0, idx + 1).every(isRouteInitialized);\n    } else {\n      initialized = initialMatches.every(isRouteInitialized);\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnMiss() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnMissFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (ignoreNextHistoryUpdate) {\n          ignoreNextHistoryUpdate = false;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          ignoreNextHistoryUpdate = true;\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked\n              init.history.go(delta);\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        unstable_viewTransitionOpts: opts.viewTransitionOpts,\n        unstable_flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.unstable_viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let { error, notFoundMatches, route } = handleDiscoverRouteError(\n          location.pathname,\n          discoverResult\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches\n      );\n      result = results[0];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let { error, notFoundMatches, route } = handleDiscoverRouteError(\n          location.pathname,\n          discoverResult\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.unstable_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      if (fetchControllers.has(rf.key)) {\n        abortFetcher(rf.key);\n      }\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect([...loaderResults, ...fetcherResults]);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      await startRedirectNavigation(request, redirect.result, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // During partial hydration, preserve SSR errors for routes that don't re-run\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      Object.entries(state.errors)\n        .filter(([id]) => !matchesToLoad.some((m) => m.route.id === id))\n        .forEach(([routeId, error]) => {\n          errors = Object.assign(errors || {}, { [routeId]: error });\n        });\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let flushSync = (opts && opts.unstable_flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        let { error } = handleDiscoverRouteError(path, discoverResult);\n        setFetcherError(key, routeId, error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      fetchRequest,\n      [match],\n      requestMatches\n    );\n    let actionResult = actionResults[0];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, {\n            fetcherSubmission: submission,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.unstable_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        if (fetchControllers.has(staleKey)) {\n          abortFetcher(staleKey);\n        }\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect([...loaderResults, ...fetcherResults]);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      return startRedirectNavigation(revalidationRequest, redirect.result);\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        path,\n        fetchRequest.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        let { error } = handleDiscoverRouteError(path, discoverResult);\n        setFetcherError(key, routeId, error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      fetchRequest,\n      [match],\n      matches\n    );\n    let result = results[0];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result);\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    {\n      submission,\n      fetcherSubmission,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[]\n  ): Promise<DataResult[]> {\n    try {\n      let results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        request,\n        matchesToLoad,\n        matches,\n        manifest,\n        mapRouteProperties\n      );\n\n      return await Promise.all(\n        results.map((result, i) => {\n          if (isRedirectHandlerResult(result)) {\n            let response = result.result as Response;\n            return {\n              type: ResultType.redirect,\n              response: normalizeRelativeRoutingRedirectResponse(\n                response,\n                request,\n                matchesToLoad[i].route.id,\n                matches,\n                basename,\n                future.v7_relativeSplatPath\n              ),\n            };\n          }\n\n          return convertHandlerResultToDataResult(result);\n        })\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      return matchesToLoad.map(() => ({\n        type: ResultType.error,\n        error: e,\n      }));\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let [loaderResults, ...fetcherResults] = await Promise.all([\n      matchesToLoad.length\n        ? callDataStrategy(\"loader\", request, matchesToLoad, matches)\n        : [],\n      ...fetchersToLoad.map((f) => {\n        if (f.matches && f.match && f.controller) {\n          let fetcherRequest = createClientSideRequest(\n            init.history,\n            f.path,\n            f.controller.signal\n          );\n          return callDataStrategy(\n            \"loader\",\n            fetcherRequest,\n            [f.match],\n            f.matches\n          ).then((r) => r[0]);\n        } else {\n          return Promise.resolve<DataResult>({\n            type: ResultType.error,\n            error: getInternalRouterError(404, {\n              pathname: f.path,\n            }),\n          });\n        }\n      }),\n    ]);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        loaderResults.map(() => request.signal),\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map((f) => f.match),\n        fetcherResults,\n        fetchersToLoad.map((f) => (f.controller ? f.controller.signal : null)),\n        true\n      ),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    if (future.v7_fetcherPersist) {\n      activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n      // If this fetcher was previously marked for deletion, unmark it since we\n      // have a new instance\n      if (deletedFetchers.has(key)) {\n        deletedFetchers.delete(key);\n      }\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    deletedFetchers.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    if (future.v7_fetcherPersist) {\n      let count = (activeFetchers.get(key) || 0) - 1;\n      if (count <= 0) {\n        activeFetchers.delete(key);\n        deletedFetchers.add(key);\n      } else {\n        activeFetchers.set(key, count);\n      }\n    } else {\n      deleteFetcher(key);\n    }\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function handleDiscoverRouteError(\n    pathname: string,\n    discoverResult: DiscoverRoutesErrorResult\n  ) {\n    let matches = discoverResult.partialMatches;\n    let route = matches[matches.length - 1].route;\n    let error = getInternalRouterError(400, {\n      type: \"route-discovery\",\n      routeId: route.id,\n      pathname,\n      message:\n        discoverResult.error != null && \"message\" in discoverResult.error\n          ? discoverResult.error\n          : String(discoverResult.error),\n    });\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnMissImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        let leafRoute = matches[matches.length - 1].route;\n        if (\n          leafRoute.path &&\n          (leafRoute.path === \"*\" || leafRoute.path.endsWith(\"/*\"))\n        ) {\n          // If we matched a splat, it might only be because we haven't yet fetched\n          // the children that would match with a higher score, so let's fetch\n          // around and find out\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal\n  ): Promise<DiscoverRoutesResult> {\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    let route =\n      partialMatches.length > 0\n        ? partialMatches[partialMatches.length - 1].route\n        : null;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      try {\n        await loadLazyRouteChildren(\n          patchRoutesOnMissImpl!,\n          pathname,\n          partialMatches,\n          routesToUse,\n          manifest,\n          mapRouteProperties,\n          pendingPatchRoutes,\n          signal\n        );\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      let matchedSplat = false;\n      if (newMatches) {\n        let leafRoute = newMatches[newMatches.length - 1].route;\n\n        if (leafRoute.index) {\n          // If we found an index route, we can stop\n          return { type: \"success\", matches: newMatches };\n        }\n\n        if (leafRoute.path && leafRoute.path.length > 0) {\n          if (leafRoute.path === \"*\") {\n            // If we found a splat route, we can't be sure there's not a\n            // higher-scoring route down some partial matches trail so we need\n            // to check that out\n            matchedSplat = true;\n          } else {\n            // If we found a non-splat route, we can stop\n            return { type: \"success\", matches: newMatches };\n          }\n        }\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // If we are no longer partially matching anything, this was either a\n      // legit splat match above, or it's a 404.  Also avoid loops if the\n      // second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        partialMatches.map((m) => m.route.id).join(\"-\") ===\n          newPartialMatches.map((m) => m.route.id).join(\"-\")\n      ) {\n        return { type: \"success\", matches: matchedSplat ? newMatches : null };\n      }\n\n      partialMatches = newPartialMatches;\n      route = partialMatches[partialMatches.length - 1].route;\n      if (route.path === \"*\") {\n        // The splat is still our most accurate partial, so run with it\n        return { type: \"success\", matches: partialMatches };\n      }\n    }\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      unstable_dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      unstable_dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      unstable_dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      unstable_dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      unstable_dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      unstable_dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          unstable_dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        unstable_dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `HandlerResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isHandlerResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        unstable_dataStrategy\n      );\n      result = results[0];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        unstable_dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      unstable_dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      unstable_dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    unstable_dataStrategy: DataStrategyFunction | null\n  ): Promise<DataResult[]> {\n    let results = await callDataStrategyImpl(\n      unstable_dataStrategy || defaultDataStrategy,\n      type,\n      request,\n      matchesToLoad,\n      matches,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    return await Promise.all(\n      results.map((result, i) => {\n        if (isRedirectHandlerResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            matchesToLoad[i].route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        return convertHandlerResultToDataResult(result);\n      })\n    );\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Add an ?index param for matched index routes if we don't already have one\n  if (\n    (to == null || to === \"\" || to === \".\") &&\n    activeRouteMatch &&\n    activeRouteMatch.route.index &&\n    !hasNakedIndexQuery(path.search)\n  ) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isInitialLoad: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[0]\n      : undefined;\n  let boundaryMatches = boundaryId\n    ? getLoaderMatchesUntilBoundary(matches, boundaryId)\n    : matches;\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (isInitialLoad) {\n      if (typeof route.loader !== \"function\" || route.loader.hydrate) {\n        return true;\n      }\n      return (\n        state.loaderData[route.id] === undefined &&\n        // Don't re-run if the loader ran and threw an error\n        (!state.errors || state.errors[route.id] === undefined)\n      );\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      unstable_actionStatus: actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial load (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      isInitialLoad ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.includes(key)) {\n      // Always revalidate if the fetcher was cancelled\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        unstable_actionStatus: actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\n/**\n * Idempotent utility to execute patchRoutesOnMiss() to lazily load route\n * definitions and update the routes/routeManifest\n */\nasync function loadLazyRouteChildren(\n  patchRoutesOnMissImpl: AgnosticPatchRoutesOnMissFunction,\n  path: string,\n  matches: AgnosticDataRouteMatch[],\n  routes: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  pendingRouteChildren: Map<string, ReturnType<typeof patchRoutesOnMissImpl>>,\n  signal: AbortSignal\n) {\n  let key = [path, ...matches.map((m) => m.route.id)].join(\"-\");\n  try {\n    let pending = pendingRouteChildren.get(key);\n    if (!pending) {\n      pending = patchRoutesOnMissImpl({\n        path,\n        matches,\n        patch: (routeId, children) => {\n          if (!signal.aborted) {\n            patchRoutesImpl(\n              routeId,\n              children,\n              routes,\n              manifest,\n              mapRouteProperties\n            );\n          }\n        },\n      });\n      pendingRouteChildren.set(key, pending);\n    }\n\n    if (pending && isPromise<AgnosticRouteObject[]>(pending)) {\n      await pending;\n    }\n  } finally {\n    pendingRouteChildren.delete(key);\n  }\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    let dataChildren = convertRoutesToDataRoutes(\n      children,\n      mapRouteProperties,\n      [routeId, \"patch\", String(route.children?.length || \"0\")],\n      manifest\n    );\n    if (route.children) {\n      route.children.push(...dataChildren);\n    } else {\n      route.children = dataChildren;\n    }\n  } else {\n    let dataChildren = convertRoutesToDataRoutes(\n      children,\n      mapRouteProperties,\n      [\"patch\", String(routesToUse.length || \"0\")],\n      manifest\n    );\n    routesToUse.push(...dataChildren);\n  }\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nfunction defaultDataStrategy(\n  opts: DataStrategyFunctionArgs\n): ReturnType<DataStrategyFunction> {\n  return Promise.all(opts.matches.map((m) => m.resolve()));\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<HandlerResult[]> {\n  let routeIdsToLoad = matchesToLoad.reduce(\n    (acc, m) => acc.add(m.route.id),\n    new Set<string>()\n  );\n  let loadedMatches = new Set<string>();\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: matches.map((match) => {\n      let shouldLoad = routeIdsToLoad.has(match.route.id);\n      // `resolve` encapsulates the route.lazy, executing the\n      // loader/action, and mapping return values/thrown errors to a\n      // HandlerResult.  Users can pass a callback to take fine-grained control\n      // over the execution of the loader/action\n      let resolve: DataStrategyMatch[\"resolve\"] = (handlerOverride) => {\n        loadedMatches.add(match.route.id);\n        return shouldLoad\n          ? callLoaderOrAction(\n              type,\n              request,\n              match,\n              manifest,\n              mapRouteProperties,\n              handlerOverride,\n              requestContext\n            )\n          : Promise.resolve({ type: ResultType.data, result: undefined });\n      };\n\n      return {\n        ...match,\n        shouldLoad,\n        resolve,\n      };\n    }),\n    request,\n    params: matches[0].params,\n    context: requestContext,\n  });\n\n  // Throw if any loadRoute implementations not called since they are what\n  // ensures a route is fully loaded\n  matches.forEach((m) =>\n    invariant(\n      loadedMatches.has(m.route.id),\n      `\\`match.resolve()\\` was not called for route id \"${m.route.id}\". ` +\n        \"You must call `match.resolve()` on every match passed to \" +\n        \"`dataStrategy` to ensure all routes are properly loaded.\"\n    )\n  );\n\n  // Filter out any middleware-only matches for which we didn't need to run handlers\n  return results.filter((_, i) => routeIdsToLoad.has(matches[i].route.id));\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<HandlerResult> {\n  let result: HandlerResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<HandlerResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<HandlerResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<HandlerResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<HandlerResult>;\n    if (handlerOverride) {\n      handlerPromise = handlerOverride((ctx: unknown) => actualHandler(ctx));\n    } else {\n      handlerPromise = (async () => {\n        try {\n          let val = await actualHandler();\n          return { type: \"data\", result: val };\n        } catch (e) {\n          return { type: \"error\", result: e };\n        }\n      })();\n    }\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    if (match.route.lazy) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadLazyRouteModule(match.route, mapRouteProperties, manifest),\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadLazyRouteModule(match.route, mapRouteProperties, manifest);\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // HandlerResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertHandlerResultToDataResult(\n  handlerResult: HandlerResult\n): Promise<DataResult> {\n  let { result, type, status } = handlerResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : status,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  return { type: ResultType.data, data: result, statusCode: status };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, match, controller } = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      continue;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\" | \"route-discovery\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (type === \"route-discovery\") {\n      errorMessage =\n        `Unable to match URL \"${pathname}\" - the \\`children()\\` function for ` +\n        `route \\`${routeId}\\` threw the following error:\\n${message}`;\n    } else if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: DataResult[]\n): { result: RedirectResult; idx: number } | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return { result, idx: i };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isHandlerResult(result: unknown): result is HandlerResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectHandlerResult(result: HandlerResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: (AgnosticDataRouteMatch | null)[],\n  results: DataResult[],\n  signals: (AbortSignal | null)[],\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      let signal = signals[index];\n      invariant(\n        signal,\n        \"Expected an AbortSignal for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n"], "names": ["Action", "PopStateEventType", "invariant", "value", "message", "Error", "warning", "cond", "console", "warn", "e", "getHistoryState", "location", "index", "usr", "state", "key", "idx", "createLocation", "current", "to", "_extends", "pathname", "search", "hash", "parsePath", "Math", "random", "toString", "substr", "createPath", "_ref", "char<PERSON>t", "path", "parsed<PERSON><PERSON>", "hashIndex", "indexOf", "searchIndex", "getUrlBasedHistory", "getLocation", "createHref", "validateLocation", "options", "window", "document", "defaultView", "v5Compat", "globalHistory", "history", "action", "Pop", "listener", "getIndex", "handlePop", "nextIndex", "delta", "createURL", "base", "origin", "href", "replace", "URL", "replaceState", "listen", "fn", "addEventListener", "removeEventListener", "encodeLocation", "url", "push", "<PERSON><PERSON>", "historyState", "pushState", "error", "DOMException", "name", "assign", "Replace", "go", "n", "ResultType", "immutableRouteKeys", "Set", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "map", "route", "treePath", "String", "id", "join", "children", "isIndexRoute", "indexRoute", "pathOrLayoutRoute", "undefined", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "sort", "a", "b", "score", "length", "slice", "every", "i", "compareIndexes", "routesMeta", "meta", "childrenIndex", "rankRouteBranches", "matches", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "caseSensitive", "startsWith", "joinPaths", "concat", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "paramRe", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "pattern", "matcher", "compiledParams", "regexpSource", "_", "paramName", "RegExp", "compilePath", "captureGroups", "memo", "splatValue", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "pop", "resolvePathname", "normalizeSearch", "normalizeHash", "getInvalidPathError", "char", "field", "dest", "JSON", "stringify", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "from", "isEmptyPath", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "paths", "Aborted<PERSON>eferredError", "DeferredData", "constructor", "responseInit", "reject", "this", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "entries", "acc", "_ref2", "trackPromise", "done", "init", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "k", "async", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "_tracked", "isTrackedPromise", "_error", "_data", "defer", "redirect", "status", "headers", "Headers", "set", "Response", "ErrorResponseImpl", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "json", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "throwStaticHandlerAbortedError", "request", "isRouteRequest", "future", "v7_throwAbortReason", "reason", "method", "normalizeTo", "prependBasename", "fromRouteId", "relative", "contextualMatches", "activeRouteMatch", "hasNakedIndexQuery", "normalizeNavigateOptions", "normalizeFormMethod", "isFetcher", "opts", "body", "isSubmissionNavigation", "isValidMethod", "getInternalRouterError", "searchParams", "getInvalidBodyError", "type", "rawFormMethod", "toUpperCase", "stripHashFromPath", "isMutationMethod", "FormData", "URLSearchParams", "_ref5", "submission", "parse", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "append", "getLoaderMatchesUntilBoundary", "boundaryId", "boundaryMatches", "findIndex", "m", "getMatchesToLoad", "isInitialLoad", "skipActionErrorRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "deletedFetchers", "fetchLoadMatches", "fetchRedirectIds", "routesToUse", "pendingActionResult", "actionResult", "isErrorResult", "currentUrl", "nextUrl", "actionStatus", "statusCode", "shouldSkipRevalidation", "navigationMatches", "lazy", "loader", "hydrate", "errors", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "unstable_actionStatus", "defaultShouldRevalidate", "isNewRouteInstance", "revalidatingFetchers", "f", "routeId", "has", "fetcherMatches", "fetcher", "fetchers", "fetcherMatch", "getTargetMatch", "shouldRevalidate", "currentPath", "loaderMatch", "arg", "routeChoice", "loadLazyRouteChildren", "patchRoutesOnMissImpl", "pendingRouteChildren", "pending", "patch", "patchRoutesImpl", "val", "_route$children", "dataChildren", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "isPropertyStaticallyDefined", "defaultDataStrategy", "all", "callDataStrategyImpl", "dataStrategyImpl", "matchesToLoad", "requestContext", "routeIdsToLoad", "loadedMatches", "results", "shouldLoad", "handlerOverride", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "handlerPromise", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "context", "handlerError", "callLoaderOrAction", "convertHandlerResultToDataResult", "handlerResult", "isResponse", "contentType", "isDeferredData", "deferred", "deferredData", "_result$init", "_result$init2", "normalizeRelativeRoutingRedirectResponse", "response", "trimmedMatches", "normalizeRedirectLocation", "normalizedLocation", "protocol", "isSameBasename", "createClientSideRequest", "Request", "processRouteLoaderData", "activeDeferreds", "skipL<PERSON>derError<PERSON><PERSON>bling", "found<PERSON><PERSON>r", "loaderHeaders", "pendingError", "isRedirectResult", "boundaryMatch", "findNearestBoundary", "isDeferredResult", "processLoaderData", "fetcherResults", "done<PERSON>etcher", "getDoneFetcher", "mergeLoaderData", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "getActionDataForCommit", "actionData", "reverse", "find", "getShortCircuitMatches", "_temp5", "errorMessage", "findRedirect", "isRedirectHandlerResult", "resolveData", "resolveDeferredResults", "currentMatches", "signals", "isRevalidatingLoader", "resolveDeferredData", "unwrap", "getAll", "getSubmissionFromNavigation", "navigation", "getLoadingNavigation", "getSubmittingNavigation", "getLoadingFetcher", "querySelector", "getAttribute", "initialEntries", "initialIndex", "entry", "createMemoryLocation", "clampIndex", "min", "max", "getCurrentLocation", "nextLocation", "splice", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "inFlightDataRoutes", "initialized", "router", "dataRoutes", "unstable_dataStrategy", "unstable_patchRoutesOnMiss", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "unstable_skipActionErrorRevalidation", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialErrors", "checkFogOfWar", "active", "isRouteInitialized", "pendingNavigationController", "historyAction", "restoreScrollPosition", "preventScrollReset", "revalidation", "Map", "blockers", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "activeFetchers", "blockerFunctions", "pendingPatchRoutes", "ignoreNextHistoryUpdate", "updateState", "newState", "completedFetchers", "deletedFetchersKeys", "unstable_viewTransitionOpts", "viewTransitionOpts", "unstable_flushSync", "flushSync", "deleteFetcher", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "_isRedirect", "keys", "priorPaths", "currentLocation", "toPaths", "getSavedScrollPosition", "startNavigation", "startUninterruptedRevalidation", "getScrollKey", "saveScrollPosition", "enableViewTransition", "loadingNavigation", "overrideNavigation", "fogOfWar", "notFoundMatches", "handleNavigational404", "isHashChangeOnly", "isFogOfWar", "interruptActiveLoads", "discoverResult", "discoverRoutes", "shortCircuited", "handleDiscoverRouteError", "actionMatch", "callDataStrategy", "startRedirectNavigation", "handleAction", "updatedMatches", "fetcherSubmission", "initialHydration", "activeSubmission", "shouldUpdateNavigationState", "getUpdatedActionData", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "rf", "revalidatingFetcher", "getUpdatedRevalidatingFetchers", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "loaderResults", "callLoadersAndMaybeResolveData", "fetcher<PERSON>ey", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "handleLoaders", "_temp2", "redirectLocation", "isDocumentReload", "redirectHistoryAction", "fetchersToLoad", "updateFetcherState", "setFetcherError", "getFetcher", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "deleteBlocker", "updateBlocker", "newBlocker", "blocker", "shouldBlockNavigation", "_ref4", "blockerKey", "blockerFunction", "partialMatch<PERSON>", "predicate", "cancelledRouteIds", "dfd", "y", "leafRoute", "isNonHMR", "newMatches", "matchedSplat", "newPartialMatches", "initialize", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "restoreAppliedTransitions", "_saveAppliedTransitions", "setItem", "persistAppliedTransitions", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "navigate", "normalizedPath", "userReplace", "unstable_viewTransition", "fetch", "requestMatches", "detectAndHandle405Error", "existingFetcher", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "handleFetcherAction", "handleFetcherLoader", "revalidate", "count", "dispose", "clear", "get<PERSON><PERSON>er", "patchRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "_internalSetRoutes", "newRoutes", "queryImpl", "routeMatch", "Location", "actionHeaders", "loaderRequest", "loadRouteData", "submit", "isHandlerResult", "isRedirectResponse", "executedLoaders", "fromEntries", "query", "_temp3", "methodNotAllowedMatches", "queryRoute", "_temp4", "values", "_result$activeDeferre", "originalPath", "prefix", "p", "array", "keyMatch", "optional", "param", "_deepestRenderedBoundaryId", "redirectDocument"], "mappings": ";;;;;;;;;;udAOYA,IAAAA,WAAAA,GAAM,OAANA,EAAM,IAAA,MAANA,EAAM,KAAA,OAANA,EAAM,QAAA,UAANA,CAAM,EAAA,IA2LlB,MAAMC,EAAoB,WAySnB,SAASC,EAAUC,EAAYC,GACpC,IAAc,IAAVD,SAAmBA,EACrB,MAAM,IAAIE,MAAMD,EAEpB,CAEO,SAASE,EAAQC,EAAWH,GACjC,IAAKG,EAAM,CAEc,oBAAZC,SAAyBA,QAAQC,KAAKL,GAEjD,IAME,MAAM,IAAIC,MAAMD,EAEL,CAAX,MAAOM,GAAI,CACf,CACF,CASA,SAASC,EAAgBC,EAAoBC,GAC3C,MAAO,CACLC,IAAKF,EAASG,MACdC,IAAKJ,EAASI,IACdC,IAAKJ,EAET,CAKO,SAASK,EACdC,EACAC,EACAL,EACAC,GAcA,YAfU,IAAVD,IAAAA,EAAa,MAGmBM,EAAA,CAC9BC,SAA6B,iBAAZH,EAAuBA,EAAUA,EAAQG,SAC1DC,OAAQ,GACRC,KAAM,IACY,iBAAPJ,EAAkBK,EAAUL,GAAMA,EAAE,CAC/CL,QAKAC,IAAMI,GAAOA,EAAgBJ,KAAQA,GAjChCU,KAAKC,SAASC,SAAS,IAAIC,OAAO,EAAG,IAoC9C,CAKO,SAASC,EAAUC,GAIR,IAJST,SACzBA,EAAW,IAAGC,OACdA,EAAS,GAAEC,KACXA,EAAO,IACOO,EAKd,OAJIR,GAAqB,MAAXA,IACZD,GAAiC,MAArBC,EAAOS,OAAO,GAAaT,EAAS,IAAMA,GACpDC,GAAiB,MAATA,IACVF,GAA+B,MAAnBE,EAAKQ,OAAO,GAAaR,EAAO,IAAMA,GAC7CF,CACT,CAKO,SAASG,EAAUQ,GACxB,IAAIC,EAA4B,CAAA,EAEhC,GAAID,EAAM,CACR,IAAIE,EAAYF,EAAKG,QAAQ,KACzBD,GAAa,IACfD,EAAWV,KAAOS,EAAKJ,OAAOM,GAC9BF,EAAOA,EAAKJ,OAAO,EAAGM,IAGxB,IAAIE,EAAcJ,EAAKG,QAAQ,KAC3BC,GAAe,IACjBH,EAAWX,OAASU,EAAKJ,OAAOQ,GAChCJ,EAAOA,EAAKJ,OAAO,EAAGQ,IAGpBJ,IACFC,EAAWZ,SAAWW,EAE1B,CAEA,OAAOC,CACT,CASA,SAASI,EACPC,EACAC,EACAC,EACAC,QAA0B,IAA1BA,IAAAA,EAA6B,CAAA,GAE7B,IAAIC,OAAEA,EAASC,SAASC,YAAYC,SAAEA,GAAW,GAAUJ,EACvDK,EAAgBJ,EAAOK,QACvBC,EAASjD,EAAOkD,IAChBC,EAA4B,KAE5BtC,EAAQuC,IASZ,SAASA,IAEP,OADYL,EAAchC,OAAS,CAAEE,IAAK,OAC7BA,GACf,CAEA,SAASoC,IACPJ,EAASjD,EAAOkD,IAChB,IAAII,EAAYF,IACZG,EAAqB,MAAbD,EAAoB,KAAOA,EAAYzC,EACnDA,EAAQyC,EACJH,GACFA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,SAEnD,CA+CA,SAASC,EAAUpC,GAIjB,IAAIqC,EACyB,SAA3Bd,EAAO/B,SAAS8C,OACZf,EAAO/B,SAAS8C,OAChBf,EAAO/B,SAAS+C,KAElBA,EAAqB,iBAAPvC,EAAkBA,EAAKU,EAAWV,GASpD,OALAuC,EAAOA,EAAKC,QAAQ,KAAM,OAC1B1D,EACEuD,EACsEE,sEAAAA,GAEjE,IAAIE,IAAIF,EAAMF,EACvB,CApFa,MAAT5C,IACFA,EAAQ,EACRkC,EAAce,aAAYzC,EAAM0B,CAAAA,EAAAA,EAAchC,MAAK,CAAEE,IAAKJ,IAAS,KAoFrE,IAAImC,EAAmB,CACjBC,aACF,OAAOA,CACR,EACGrC,eACF,OAAO2B,EAAYI,EAAQI,EAC5B,EACDgB,OAAOC,GACL,GAAIb,EACF,MAAM,IAAI9C,MAAM,8CAKlB,OAHAsC,EAAOsB,iBAAiBhE,EAAmBoD,GAC3CF,EAAWa,EAEJ,KACLrB,EAAOuB,oBAAoBjE,EAAmBoD,GAC9CF,EAAW,IAAI,CAElB,EACDX,WAAWpB,GACFoB,EAAWG,EAAQvB,GAE5BoC,YACAW,eAAe/C,GAEb,IAAIgD,EAAMZ,EAAUpC,GACpB,MAAO,CACLE,SAAU8C,EAAI9C,SACdC,OAAQ6C,EAAI7C,OACZC,KAAM4C,EAAI5C,KAEb,EACD6C,KAlGF,SAAcjD,EAAQL,GACpBkC,EAASjD,EAAOsE,KAChB,IAAI1D,EAAWM,EAAe8B,EAAQpC,SAAUQ,EAAIL,GAChD0B,GAAkBA,EAAiB7B,EAAUQ,GAEjDP,EAAQuC,IAAa,EACrB,IAAImB,EAAe5D,EAAgBC,EAAUC,GACzCuD,EAAMpB,EAAQR,WAAW5B,GAG7B,IACEmC,EAAcyB,UAAUD,EAAc,GAAIH,EAY5C,CAXE,MAAOK,GAKP,GAAIA,aAAiBC,cAA+B,mBAAfD,EAAME,KACzC,MAAMF,EAIR9B,EAAO/B,SAASgE,OAAOR,EACzB,CAEItB,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,MAAO,GAE1D,EAuEEK,QArEF,SAAiBxC,EAAQL,GACvBkC,EAASjD,EAAO6E,QAChB,IAAIjE,EAAWM,EAAe8B,EAAQpC,SAAUQ,EAAIL,GAChD0B,GAAkBA,EAAiB7B,EAAUQ,GAEjDP,EAAQuC,IACR,IAAImB,EAAe5D,EAAgBC,EAAUC,GACzCuD,EAAMpB,EAAQR,WAAW5B,GAC7BmC,EAAce,aAAaS,EAAc,GAAIH,GAEzCtB,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAUoC,EAAQpC,SAAU2C,MAAO,GAE1D,EAyDEuB,GAAGC,GACMhC,EAAc+B,GAAGC,IAI5B,OAAO/B,CACT,CC7tBYgC,IAAAA,WAAAA,GAAU,OAAVA,EAAU,KAAA,OAAVA,EAAU,SAAA,WAAVA,EAAU,SAAA,WAAVA,EAAU,MAAA,QAAVA,CAAU,EAAA,CAAA,GAwRf,MAAMC,EAAqB,IAAIC,IAAuB,CAC3D,OACA,gBACA,OACA,KACA,QACA,aA6JK,SAASC,EACdC,EACAC,EACAC,EACAC,GAEA,YAHoB,IAApBD,IAAAA,EAAuB,SACA,IAAvBC,IAAAA,EAA0B,CAAA,GAEnBH,EAAOI,KAAI,CAACC,EAAO5E,KACxB,IAAI6E,EAAW,IAAIJ,EAAYK,OAAO9E,IAClC+E,EAAyB,iBAAbH,EAAMG,GAAkBH,EAAMG,GAAKF,EAASG,KAAK,KAWjE,GAVA3F,GACkB,IAAhBuF,EAAM5E,QAAmB4E,EAAMK,SAAQ,6CAGzC5F,GACGqF,EAASK,GACV,qCAAqCA,EAArC,qEAvBN,SACEH,GAEA,OAAuB,IAAhBA,EAAM5E,KACf,CAuBQkF,CAAaN,GAAQ,CACvB,IAAIO,EAAwC3E,EAAA,CAAA,EACvCoE,EACAJ,EAAmBI,GAAM,CAC5BG,OAGF,OADAL,EAASK,GAAMI,EACRA,CACT,CAAO,CACL,IAAIC,EAAkD5E,EAAA,CAAA,EACjDoE,EACAJ,EAAmBI,GAAM,CAC5BG,KACAE,cAAUI,IAaZ,OAXAX,EAASK,GAAMK,EAEXR,EAAMK,WACRG,EAAkBH,SAAWX,EAC3BM,EAAMK,SACNT,EACAK,EACAH,IAIGU,CACT,IAEJ,CAOO,SAASE,EAGdf,EACAgB,EACAC,GAEA,YAFQ,IAARA,IAAAA,EAAW,KAEJC,EAAgBlB,EAAQgB,EAAaC,GAAU,EACxD,CAEO,SAASC,EAGdlB,EACAgB,EACAC,EACAE,GAEA,IAGIjF,EAAWkF,GAFU,iBAAhBJ,EAA2B3E,EAAU2E,GAAeA,GAEvB9E,UAAY,IAAK+E,GAEvD,GAAgB,MAAZ/E,EACF,OAAO,KAGT,IAAImF,EAAWC,EAActB,IAmM/B,SAA2BqB,GACzBA,EAASE,MAAK,CAACC,EAAGC,IAChBD,EAAEE,QAAUD,EAAEC,MACVD,EAAEC,MAAQF,EAAEE,MAyCpB,SAAwBF,EAAaC,GAInC,OAFED,EAAEG,SAAWF,EAAEE,QAAUH,EAAEI,MAAM,GAAI,GAAGC,OAAM,CAAClC,EAAGmC,IAAMnC,IAAM8B,EAAEK,KAO9DN,EAAEA,EAAEG,OAAS,GAAKF,EAAEA,EAAEE,OAAS,GAG/B,CACN,CArDQI,CACEP,EAAEQ,WAAW5B,KAAK6B,GAASA,EAAKC,gBAChCT,EAAEO,WAAW5B,KAAK6B,GAASA,EAAKC,kBAG1C,CA3MEC,CAAkBd,GAElB,IAAIe,EAAU,KACd,IAAK,IAAIN,EAAI,EAAc,MAAXM,GAAmBN,EAAIT,EAASM,SAAUG,EAAG,CAO3D,IAAIO,EAAUC,EAAWpG,GACzBkG,EAAUG,EACRlB,EAASS,GACTO,EACAlB,EAEJ,CAEA,OAAOiB,CACT,CAUO,SAASI,EACdC,EACAC,GAEA,IAAIrC,MAAEA,EAAKnE,SAAEA,EAAQyG,OAAEA,GAAWF,EAClC,MAAO,CACLjC,GAAIH,EAAMG,GACVtE,WACAyG,SACAC,KAAMF,EAAWrC,EAAMG,IACvBqC,OAAQxC,EAAMwC,OAElB,CAmBA,SAASvB,EAGPtB,EACAqB,EACAyB,EACA5C,QAFwC,IAAxCmB,IAAAA,EAA2C,SACF,IAAzCyB,IAAAA,EAA4C,SAClC,IAAV5C,IAAAA,EAAa,IAEb,IAAI6C,EAAeA,CACjB1C,EACA5E,EACAuH,KAEA,IAAIf,EAAmC,CACrCe,kBACmBlC,IAAjBkC,EAA6B3C,EAAMxD,MAAQ,GAAKmG,EAClDC,eAAuC,IAAxB5C,EAAM4C,cACrBf,cAAezG,EACf4E,SAGE4B,EAAKe,aAAaE,WAAW,OAC/BpI,EACEmH,EAAKe,aAAaE,WAAWhD,GAC7B,wBAAwB+B,EAAKe,aAA7B,wBACM9C,EADN,4GAKF+B,EAAKe,aAAef,EAAKe,aAAapB,MAAM1B,EAAWyB,SAGzD,IAAI9E,EAAOsG,EAAU,CAACjD,EAAY+B,EAAKe,eACnChB,EAAac,EAAYM,OAAOnB,GAKhC5B,EAAMK,UAAYL,EAAMK,SAASiB,OAAS,IAC5C7G,GAGkB,IAAhBuF,EAAM5E,MACN,4FACuCoB,QAEzCyE,EAAcjB,EAAMK,SAAUW,EAAUW,EAAYnF,KAKpC,MAAdwD,EAAMxD,MAAiBwD,EAAM5E,QAIjC4F,EAASpC,KAAK,CACZpC,OACA6E,MAAO2B,EAAaxG,EAAMwD,EAAM5E,OAChCuG,cACA,EAaJ,OAXAhC,EAAOsD,SAAQ,CAACjD,EAAO5E,KAAU,IAAA8H,EAE/B,GAAmB,KAAflD,EAAMxD,aAAe0G,EAAClD,EAAMxD,OAAN0G,EAAYC,SAAS,KAG7C,IAAK,IAAIC,KAAYC,EAAwBrD,EAAMxD,MACjDkG,EAAa1C,EAAO5E,EAAOgI,QAH7BV,EAAa1C,EAAO5E,EAKtB,IAGK4F,CACT,CAgBA,SAASqC,EAAwB7G,GAC/B,IAAI8G,EAAW9G,EAAK+G,MAAM,KAC1B,GAAwB,IAApBD,EAAShC,OAAc,MAAO,GAElC,IAAKkC,KAAUC,GAAQH,EAGnBI,EAAaF,EAAMG,SAAS,KAE5BC,EAAWJ,EAAMrF,QAAQ,MAAO,IAEpC,GAAoB,IAAhBsF,EAAKnC,OAGP,OAAOoC,EAAa,CAACE,EAAU,IAAM,CAACA,GAGxC,IAAIC,EAAeR,EAAwBI,EAAKrD,KAAK,MAEjD0D,EAAmB,GAqBvB,OAZAA,EAAOlF,QACFiF,EAAa9D,KAAKgE,GACP,KAAZA,EAAiBH,EAAW,CAACA,EAAUG,GAAS3D,KAAK,QAKrDsD,GACFI,EAAOlF,QAAQiF,GAIVC,EAAO/D,KAAKqD,GACjB5G,EAAKqG,WAAW,MAAqB,KAAbO,EAAkB,IAAMA,GAEpD,CAaA,MAAMY,EAAU,YAMVC,EAAWC,GAAoB,MAANA,EAE/B,SAASlB,EAAaxG,EAAcpB,GAClC,IAAIkI,EAAW9G,EAAK+G,MAAM,KACtBY,EAAeb,EAAShC,OAS5B,OARIgC,EAASc,KAAKH,KAChBE,IAPiB,GAUf/I,IACF+I,GAdoB,GAiBfb,EACJe,QAAQH,IAAOD,EAAQC,KACvBI,QACC,CAACjD,EAAOkD,IACNlD,GACC2C,EAAQQ,KAAKD,GAvBM,EAyBJ,KAAZA,EAvBc,EACC,KAyBrBJ,EAEN,CAiBA,SAASjC,EAIPuC,EACA5I,EACAiF,QAAY,IAAZA,IAAAA,GAAe,GAEf,IAAIa,WAAEA,GAAe8C,EAEjBC,EAAgB,CAAA,EAChBC,EAAkB,IAClB5C,EAA2D,GAC/D,IAAK,IAAIN,EAAI,EAAGA,EAAIE,EAAWL,SAAUG,EAAG,CAC1C,IAAIG,EAAOD,EAAWF,GAClBmD,EAAMnD,IAAME,EAAWL,OAAS,EAChCuD,EACkB,MAApBF,EACI9I,EACAA,EAAS0F,MAAMoD,EAAgBrD,SAAW,IAC5Cc,EAAQ0C,EACV,CAAEtI,KAAMoF,EAAKe,aAAcC,cAAehB,EAAKgB,cAAegC,OAC9DC,GAGE7E,EAAQ4B,EAAK5B,MAkBjB,IAfGoC,GACDwC,GACA9D,IACCa,EAAWA,EAAWL,OAAS,GAAGtB,MAAM5E,QAEzCgH,EAAQ0C,EACN,CACEtI,KAAMoF,EAAKe,aACXC,cAAehB,EAAKgB,cACpBgC,KAAK,GAEPC,KAICzC,EACH,OAAO,KAGT2C,OAAO5F,OAAOuF,EAAetC,EAAME,QAEnCP,EAAQnD,KAAK,CAEX0D,OAAQoC,EACR7I,SAAUiH,EAAU,CAAC6B,EAAiBvC,EAAMvG,WAC5CmJ,aAAcC,EACZnC,EAAU,CAAC6B,EAAiBvC,EAAM4C,gBAEpChF,UAGyB,MAAvBoC,EAAM4C,eACRL,EAAkB7B,EAAU,CAAC6B,EAAiBvC,EAAM4C,eAExD,CAEA,OAAOjD,CACT,CAiHO,SAAS+C,EAIdI,EACArJ,GAEuB,iBAAZqJ,IACTA,EAAU,CAAE1I,KAAM0I,EAAStC,eAAe,EAAOgC,KAAK,IAGxD,IAAKO,EAASC,GA4ChB,SACE5I,EACAoG,EACAgC,QADa,IAAbhC,IAAAA,GAAgB,QACb,IAAHgC,IAAAA,GAAM,GAEN/J,EACW,MAAT2B,IAAiBA,EAAKmH,SAAS,MAAQnH,EAAKmH,SAAS,MACrD,eAAenH,EAAf,oCACMA,EAAK2B,QAAQ,MAAO,MAD1B,qIAGsC3B,EAAK2B,QAAQ,MAAO,YAG5D,IAAImE,EAA8B,GAC9B+C,EACF,IACA7I,EACG2B,QAAQ,UAAW,IACnBA,QAAQ,OAAQ,KAChBA,QAAQ,qBAAsB,QAC9BA,QACC,qBACA,CAACmH,EAAWC,EAAmB7B,KAC7BpB,EAAO1D,KAAK,CAAE2G,YAAW7B,WAA0B,MAAdA,IAC9BA,EAAa,eAAiB,gBAIzClH,EAAKmH,SAAS,MAChBrB,EAAO1D,KAAK,CAAE2G,UAAW,MACzBF,GACW,MAAT7I,GAAyB,OAATA,EACZ,QACA,qBACGoI,EAETS,GAAgB,QACE,KAAT7I,GAAwB,MAATA,IAQxB6I,GAAgB,iBAOlB,MAAO,CAFO,IAAIG,OAAOH,EAAczC,OAAgBnC,EAAY,KAElD6B,EACnB,CAjGkCmD,CAC9BP,EAAQ1I,KACR0I,EAAQtC,cACRsC,EAAQN,KAGNxC,EAAQvG,EAASuG,MAAM+C,GAC3B,IAAK/C,EAAO,OAAO,KAEnB,IAAIuC,EAAkBvC,EAAM,GACxB4C,EAAeL,EAAgBxG,QAAQ,UAAW,MAClDuH,EAAgBtD,EAAMb,MAAM,GAuBhC,MAAO,CACLe,OAvBmB8C,EAAed,QAClC,CAACqB,EAAIrJ,EAA6BlB,KAAU,IAArCmK,UAAEA,EAAS7B,WAAEA,GAAYpH,EAG9B,GAAkB,MAAdiJ,EAAmB,CACrB,IAAIK,EAAaF,EAActK,IAAU,GACzC4J,EAAeL,EACZpD,MAAM,EAAGoD,EAAgBrD,OAASsE,EAAWtE,QAC7CnD,QAAQ,UAAW,KACxB,CAEA,MAAMzD,EAAQgL,EAActK,GAM5B,OAJEuK,EAAKJ,GADH7B,IAAehJ,OACC+F,GAEC/F,GAAS,IAAIyD,QAAQ,OAAQ,KAE3CwH,CAAI,GAEb,CACF,GAIE9J,SAAU8I,EACVK,eACAE,UAEJ,CA2DA,SAASjD,EAAWvH,GAClB,IACE,OAAOA,EACJ6I,MAAM,KACNxD,KAAK8F,GAAMC,mBAAmBD,GAAG1H,QAAQ,MAAO,SAChDiC,KAAK,IAUV,CATE,MAAOpB,GAQP,OAPAnE,GACE,EACA,iBAAiBH,EAAjB,oHAEesE,EAAK,MAGftE,CACT,CACF,CAKO,SAASqG,EACdlF,EACA+E,GAEA,GAAiB,MAAbA,EAAkB,OAAO/E,EAE7B,IAAKA,EAASkK,cAAclD,WAAWjC,EAASmF,eAC9C,OAAO,KAKT,IAAIC,EAAapF,EAAS+C,SAAS,KAC/B/C,EAASU,OAAS,EAClBV,EAASU,OACT2E,EAAWpK,EAASU,OAAOyJ,GAC/B,OAAIC,GAAyB,MAAbA,EAEP,KAGFpK,EAAS0F,MAAMyE,IAAe,GACvC,CAOO,SAASE,EAAYvK,EAAQwK,QAAY,IAAZA,IAAAA,EAAe,KACjD,IACEtK,SAAUuK,EAAUtK,OACpBA,EAAS,GAAEC,KACXA,EAAO,IACS,iBAAPJ,EAAkBK,EAAUL,GAAMA,EAEzCE,EAAWuK,EACXA,EAAWvD,WAAW,KACpBuD,EAWR,SAAyBzD,EAAsBwD,GAC7C,IAAI7C,EAAW6C,EAAahI,QAAQ,OAAQ,IAAIoF,MAAM,KAYtD,OAXuBZ,EAAaY,MAAM,KAEzBN,SAASsB,IACR,OAAZA,EAEEjB,EAAShC,OAAS,GAAGgC,EAAS+C,MACb,MAAZ9B,GACTjB,EAAS1E,KAAK2F,EAChB,IAGKjB,EAAShC,OAAS,EAAIgC,EAASlD,KAAK,KAAO,GACpD,CAxBQkG,CAAgBF,EAAYD,GAC9BA,EAEJ,MAAO,CACLtK,WACAC,OAAQyK,EAAgBzK,GACxBC,KAAMyK,EAAczK,GAExB,CAkBA,SAAS0K,EACPC,EACAC,EACAC,EACApK,GAEA,MACE,qBAAqBkK,EAArB,2CACQC,cAAkBE,KAAKC,UAC7BtK,GAFF,yCAIQoK,EAJR,2HAOJ,CAyBO,SAASG,EAEdhF,GACA,OAAOA,EAAQsC,QACb,CAACjC,EAAOhH,IACI,IAAVA,GAAgBgH,EAAMpC,MAAMxD,MAAQ4F,EAAMpC,MAAMxD,KAAK8E,OAAS,GAEpE,CAIO,SAAS0F,EAEdjF,EAAckF,GACd,IAAIC,EAAcH,EAA2BhF,GAK7C,OAAIkF,EACKC,EAAYnH,KAAI,CAACqC,EAAO5G,IAC7BA,IAAQ0L,EAAY5F,OAAS,EAAIc,EAAMvG,SAAWuG,EAAM4C,eAIrDkC,EAAYnH,KAAKqC,GAAUA,EAAM4C,cAC1C,CAKO,SAASmC,EACdC,EACAC,EACAC,EACAC,GAEA,IAAI5L,OAFU,IAAd4L,IAAAA,GAAiB,GAGI,iBAAVH,EACTzL,EAAKK,EAAUoL,IAEfzL,EAAEC,EAAQwL,GAAAA,GAEV3M,GACGkB,EAAGE,WAAaF,EAAGE,SAASsH,SAAS,KACtCsD,EAAoB,IAAK,WAAY,SAAU9K,IAEjDlB,GACGkB,EAAGE,WAAaF,EAAGE,SAASsH,SAAS,KACtCsD,EAAoB,IAAK,WAAY,OAAQ9K,IAE/ClB,GACGkB,EAAGG,SAAWH,EAAGG,OAAOqH,SAAS,KAClCsD,EAAoB,IAAK,SAAU,OAAQ9K,KAI/C,IAGI6L,EAHAC,EAAwB,KAAVL,GAAgC,KAAhBzL,EAAGE,SACjCuK,EAAaqB,EAAc,IAAM9L,EAAGE,SAaxC,GAAkB,MAAduK,EACFoB,EAAOF,MACF,CACL,IAAII,EAAqBL,EAAe/F,OAAS,EAMjD,IAAKiG,GAAkBnB,EAAWvD,WAAW,MAAO,CAClD,IAAI8E,EAAavB,EAAW7C,MAAM,KAElC,KAAyB,OAAlBoE,EAAW,IAChBA,EAAWC,QACXF,GAAsB,EAGxB/L,EAAGE,SAAW8L,EAAWvH,KAAK,IAChC,CAEAoH,EAAOE,GAAsB,EAAIL,EAAeK,GAAsB,GACxE,CAEA,IAAIlL,EAAO0J,EAAYvK,EAAI6L,GAGvBK,EACFzB,GAA6B,MAAfA,GAAsBA,EAAWzC,SAAS,KAEtDmE,GACDL,GAA8B,MAAfrB,IAAuBkB,EAAiB3D,SAAS,KAQnE,OANGnH,EAAKX,SAAS8H,SAAS,OACvBkE,IAA4BC,IAE7BtL,EAAKX,UAAY,KAGZW,CACT,OAiBasG,EAAaiF,GACxBA,EAAM3H,KAAK,KAAKjC,QAAQ,SAAU,KAKvB8G,EAAqBpJ,GAChCA,EAASsC,QAAQ,OAAQ,IAAIA,QAAQ,OAAQ,KAKlCoI,EAAmBzK,GAC7BA,GAAqB,MAAXA,EAEPA,EAAO+G,WAAW,KAClB/G,EACA,IAAMA,EAHN,GAQO0K,EAAiBzK,GAC3BA,GAAiB,MAATA,EAAoBA,EAAK8G,WAAW,KAAO9G,EAAO,IAAMA,EAAzC,GA+BnB,MAAMiM,UAA6BpN,OAEnC,MAAMqN,EAWXC,YAAY3F,EAA+B4F,GAQzC,IAAIC,EARkEC,KAVhEC,eAA8B,IAAI7I,IAAa4I,KAI/CE,YACN,IAAI9I,IAAK4I,KAGXG,aAAyB,GAGvB/N,EACE8H,GAAwB,iBAATA,IAAsBkG,MAAMC,QAAQnG,GACnD,sCAMF8F,KAAKM,aAAe,IAAIC,SAAQ,CAACtD,EAAGuD,IAAOT,EAASS,IACpDR,KAAKS,WAAa,IAAIC,gBACtB,IAAIC,EAAUA,IACZZ,EAAO,IAAIJ,EAAqB,0BAClCK,KAAKY,oBAAsB,IACzBZ,KAAKS,WAAWI,OAAOzK,oBAAoB,QAASuK,GACtDX,KAAKS,WAAWI,OAAO1K,iBAAiB,QAASwK,GAEjDX,KAAK9F,KAAOwC,OAAOoE,QAAQ5G,GAAM+B,QAC/B,CAAC8E,EAAGC,KAAA,IAAG9N,EAAKb,GAAM2O,EAAA,OAChBtE,OAAO5F,OAAOiK,EAAK,CACjB7N,CAACA,GAAM8M,KAAKiB,aAAa/N,EAAKb,IAC9B,GACJ,CACF,GAEI2N,KAAKkB,MAEPlB,KAAKY,sBAGPZ,KAAKmB,KAAOrB,CACd,CAEQmB,aACN/N,EACAb,GAEA,KAAMA,aAAiBkO,SACrB,OAAOlO,EAGT2N,KAAKG,aAAa5J,KAAKrD,GACvB8M,KAAKC,eAAemB,IAAIlO,GAIxB,IAAImO,EAA0Bd,QAAQe,KAAK,CAACjP,EAAO2N,KAAKM,eAAeiB,MACpErH,GAAS8F,KAAKwB,SAASH,EAASnO,OAAKkF,EAAW8B,KAChDvD,GAAUqJ,KAAKwB,SAASH,EAASnO,EAAKyD,KAQzC,OAHA0K,EAAQI,OAAM,SAEd/E,OAAOgF,eAAeL,EAAS,WAAY,CAAEM,IAAKA,KAAM,IACjDN,CACT,CAEQG,SACNH,EACAnO,EACAyD,EACAuD,GAEA,GACE8F,KAAKS,WAAWI,OAAOe,SACvBjL,aAAiBgJ,EAIjB,OAFAK,KAAKY,sBACLlE,OAAOgF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMhL,IAC/C4J,QAAQR,OAAOpJ,GAYxB,GATAqJ,KAAKC,eAAe4B,OAAO3O,GAEvB8M,KAAKkB,MAEPlB,KAAKY,2BAKOxI,IAAVzB,QAAgCyB,IAAT8B,EAAoB,CAC7C,IAAI4H,EAAiB,IAAIvP,MACvB,0BAA0BW,EAA1B,yFAKF,OAFAwJ,OAAOgF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMG,IACtD9B,KAAK+B,MAAK,EAAO7O,GACVqN,QAAQR,OAAO+B,EACxB,CAEA,YAAa1J,IAAT8B,GACFwC,OAAOgF,eAAeL,EAAS,SAAU,CAAEM,IAAKA,IAAMhL,IACtDqJ,KAAK+B,MAAK,EAAO7O,GACVqN,QAAQR,OAAOpJ,KAGxB+F,OAAOgF,eAAeL,EAAS,QAAS,CAAEM,IAAKA,IAAMzH,IACrD8F,KAAK+B,MAAK,EAAO7O,GACVgH,EACT,CAEQ6H,KAAKH,EAAkBI,GAC7BhC,KAAKE,YAAYtF,SAASqH,GAAeA,EAAWL,EAASI,IAC/D,CAEAE,UAAUhM,GAER,OADA8J,KAAKE,YAAYkB,IAAIlL,GACd,IAAM8J,KAAKE,YAAY2B,OAAO3L,EACvC,CAEAiM,SACEnC,KAAKS,WAAW2B,QAChBpC,KAAKC,eAAerF,SAAQ,CAAC4C,EAAG6E,IAAMrC,KAAKC,eAAe4B,OAAOQ,KACjErC,KAAK+B,MAAK,EACZ,CAEAO,kBAAkBzB,GAChB,IAAIe,GAAU,EACd,IAAK5B,KAAKkB,KAAM,CACd,IAAIP,EAAUA,IAAMX,KAAKmC,SACzBtB,EAAO1K,iBAAiB,QAASwK,GACjCiB,QAAgB,IAAIrB,SAASgC,IAC3BvC,KAAKkC,WAAWN,IACdf,EAAOzK,oBAAoB,QAASuK,IAChCiB,GAAW5B,KAAKkB,OAClBqB,EAAQX,EACV,GACA,GAEN,CACA,OAAOA,CACT,CAEIV,WACF,OAAoC,IAA7BlB,KAAKC,eAAeuC,IAC7B,CAEIC,oBAMF,OALArQ,EACgB,OAAd4N,KAAK9F,MAAiB8F,KAAKkB,KAC3B,6DAGKxE,OAAOoE,QAAQd,KAAK9F,MAAM+B,QAC/B,CAAC8E,EAAG2B,KAAA,IAAGxP,EAAKb,GAAMqQ,EAAA,OAChBhG,OAAO5F,OAAOiK,EAAK,CACjB7N,CAACA,GAAMyP,EAAqBtQ,IAC5B,GACJ,CACF,EACF,CAEIuQ,kBACF,OAAOxC,MAAMjB,KAAKa,KAAKC,eACzB,EASF,SAAS0C,EAAqBtQ,GAC5B,IAPF,SAA0BA,GACxB,OACEA,aAAiBkO,UAAkD,IAAtClO,EAAyBwQ,QAE1D,CAGOC,CAAiBzQ,GACpB,OAAOA,EAGT,GAAIA,EAAM0Q,OACR,MAAM1Q,EAAM0Q,OAEd,OAAO1Q,EAAM2Q,KACf,CAOaC,MAeAC,EAA6B,SAAC5M,EAAK6K,QAAI,IAAJA,IAAAA,EAAO,KACrD,IAAIrB,EAAeqB,EACS,iBAAjBrB,EACTA,EAAe,CAAEqD,OAAQrD,QACe,IAAxBA,EAAaqD,SAC7BrD,EAAaqD,OAAS,KAGxB,IAAIC,EAAU,IAAIC,QAAQvD,EAAasD,SAGvC,OAFAA,EAAQE,IAAI,WAAYhN,GAEjB,IAAIiN,SAAS,KAAIhQ,KACnBuM,EAAY,CACfsD,YAEJ,EA2BO,MAAMI,EAOX3D,YACEsD,EACAM,EACAvJ,EACAwJ,QAAQ,IAARA,IAAAA,GAAW,GAEX1D,KAAKmD,OAASA,EACdnD,KAAKyD,WAAaA,GAAc,GAChCzD,KAAK0D,SAAWA,EACZxJ,aAAgB3H,OAClByN,KAAK9F,KAAOA,EAAKpG,WACjBkM,KAAKrJ,MAAQuD,GAEb8F,KAAK9F,KAAOA,CAEhB,EAOK,SAASyJ,EAAqBhN,GACnC,OACW,MAATA,GACwB,iBAAjBA,EAAMwM,QACe,iBAArBxM,EAAM8M,YACa,kBAAnB9M,EAAM+M,UACb,SAAU/M,CAEd,CCp9BA,MAAMiN,EAAgD,CACpD,OACA,MACA,QACA,UAEIC,EAAuB,IAAIzM,IAC/BwM,GAGIE,EAAuC,CAC3C,SACGF,GAECG,EAAsB,IAAI3M,IAAgB0M,GAE1CE,EAAsB,IAAI5M,IAAI,CAAC,IAAK,IAAK,IAAK,IAAK,MACnD6M,EAAoC,IAAI7M,IAAI,CAAC,IAAK,MAE3C8M,EAA4C,CACvDjR,MAAO,OACPH,cAAUsF,EACV+L,gBAAY/L,EACZgM,gBAAYhM,EACZiM,iBAAajM,EACbkM,cAAUlM,EACVmM,UAAMnM,EACNoM,UAAMpM,GAGKqM,EAAsC,CACjDxR,MAAO,OACPiH,UAAM9B,EACN+L,gBAAY/L,EACZgM,gBAAYhM,EACZiM,iBAAajM,EACbkM,cAAUlM,EACVmM,UAAMnM,EACNoM,UAAMpM,GAGKsM,EAAiC,CAC5CzR,MAAO,YACP0R,aAASvM,EACTwM,WAAOxM,EACPtF,cAAUsF,GAGNyM,EAAqB,gCAErBC,EAAyDnN,IAAW,CACxEoN,iBAAkBC,QAAQrN,EAAMoN,oBAG5BE,EAA0B,iCAklFnBC,EAAyBC,OAAO,YA8oB7C,SAASC,GACPC,EACAC,EACAC,GAEA,GAAIA,EAAOC,0BAAiDpN,IAA1BiN,EAAQxE,OAAO4E,OAC/C,MAAMJ,EAAQxE,OAAO4E,OAIvB,MAAM,IAAIlT,OADG+S,EAAiB,aAAe,SACAD,oBAAAA,EAAQK,OAAUL,IAAAA,EAAQ/O,IACzE,CAYA,SAASqP,GACP7S,EACA4G,EACAnB,EACAqN,EACAtS,EACAsL,EACAiH,EACAC,GAEA,IAAIC,EACAC,EACJ,GAAIH,EAAa,CAGfE,EAAoB,GACpB,IAAK,IAAIhM,KAASL,EAEhB,GADAqM,EAAkBxP,KAAKwD,GACnBA,EAAMpC,MAAMG,KAAO+N,EAAa,CAClCG,EAAmBjM,EACnB,KACF,CAEJ,MACEgM,EAAoBrM,EACpBsM,EAAmBtM,EAAQA,EAAQT,OAAS,GAI9C,IAAI9E,EAAO2K,EACTxL,GAAU,IACVqL,EAAoBoH,EAAmBnH,GACvClG,EAAc5F,EAASU,SAAU+E,IAAazF,EAASU,SAC1C,SAAbsS,GAgCF,OA1BU,MAANxS,IACFa,EAAKV,OAASX,EAASW,OACvBU,EAAKT,KAAOZ,EAASY,MAKd,MAANJ,GAAqB,KAAPA,GAAoB,MAAPA,IAC5B0S,IACAA,EAAiBrO,MAAM5E,OACtBkT,GAAmB9R,EAAKV,UAEzBU,EAAKV,OAASU,EAAKV,OACfU,EAAKV,OAAOqC,QAAQ,MAAO,WAC3B,UAOF8P,GAAgC,MAAbrN,IACrBpE,EAAKX,SACe,MAAlBW,EAAKX,SAAmB+E,EAAWkC,EAAU,CAAClC,EAAUpE,EAAKX,YAG1DQ,EAAWG,EACpB,CAIA,SAAS+R,GACPC,EACAC,EACAjS,EACAkS,GAOA,IAAKA,IA3FP,SACEA,GAEA,OACU,MAARA,IACE,aAAcA,GAAyB,MAAjBA,EAAK/B,UAC1B,SAAU+B,QAAsBjO,IAAdiO,EAAKC,KAE9B,CAmFgBC,CAAuBF,GACnC,MAAO,CAAElS,QAGX,GAAIkS,EAAKlC,aAAeqC,GAAcH,EAAKlC,YACzC,MAAO,CACLhQ,OACAwC,MAAO8P,GAAuB,IAAK,CAAEf,OAAQW,EAAKlC,cAItD,IA0EIuC,EACApC,EA3EAqC,EAAsBA,KAAO,CAC/BxS,OACAwC,MAAO8P,GAAuB,IAAK,CAAEG,KAAM,mBAIzCC,EAAgBR,EAAKlC,YAAc,MACnCA,EAAagC,EACZU,EAAcC,cACdD,EAAcnJ,cACf0G,EAAa2C,GAAkB5S,GAEnC,QAAkBiE,IAAdiO,EAAKC,KAAoB,CAC3B,GAAyB,eAArBD,EAAKhC,YAA8B,CAErC,IAAK2C,GAAiB7C,GACpB,OAAOwC,IAGT,IAAInC,EACmB,iBAAd6B,EAAKC,KACRD,EAAKC,KACLD,EAAKC,gBAAgBW,UACrBZ,EAAKC,gBAAgBY,gBAErB9G,MAAMjB,KAAKkH,EAAKC,KAAKxF,WAAW7E,QAC9B,CAAC8E,EAAGoG,KAAA,IAAGtQ,EAAMxE,GAAM8U,EAAA,MAAA,GAAQpG,EAAMlK,EAAI,IAAIxE,EAAK,IAAA,GAC9C,IAEFwF,OAAOwO,EAAKC,MAElB,MAAO,CACLnS,OACAiT,WAAY,CACVjD,aACAC,aACAC,YAAagC,EAAKhC,YAClBC,cAAUlM,EACVmM,UAAMnM,EACNoM,QAGN,CAAO,GAAyB,qBAArB6B,EAAKhC,YAAoC,CAElD,IAAK2C,GAAiB7C,GACpB,OAAOwC,IAGT,IACE,IAAIpC,EACmB,iBAAd8B,EAAKC,KAAoB9H,KAAK6I,MAAMhB,EAAKC,MAAQD,EAAKC,KAE/D,MAAO,CACLnS,OACAiT,WAAY,CACVjD,aACAC,aACAC,YAAagC,EAAKhC,YAClBC,cAAUlM,EACVmM,OACAC,UAAMpM,GAKZ,CAFE,MAAOxF,GACP,OAAO+T,GACT,CACF,CACF,CAUA,GARAvU,EACsB,mBAAb6U,SACP,iDAMEZ,EAAK/B,SACPoC,EAAeY,GAA8BjB,EAAK/B,UAClDA,EAAW+B,EAAK/B,cACX,GAAI+B,EAAKC,gBAAgBW,SAC9BP,EAAeY,GAA8BjB,EAAKC,MAClDhC,EAAW+B,EAAKC,UACX,GAAID,EAAKC,gBAAgBY,gBAC9BR,EAAeL,EAAKC,KACpBhC,EAAWiD,GAA8Bb,QACpC,GAAiB,MAAbL,EAAKC,KACdI,EAAe,IAAIQ,gBACnB5C,EAAW,IAAI2C,cAEf,IACEP,EAAe,IAAIQ,gBAAgBb,EAAKC,MACxChC,EAAWiD,GAA8Bb,EAG3C,CAFE,MAAO9T,GACP,OAAO+T,GACT,CAGF,IAAIS,EAAyB,CAC3BjD,aACAC,aACAC,YACGgC,GAAQA,EAAKhC,aAAgB,oCAChCC,WACAC,UAAMnM,EACNoM,UAAMpM,GAGR,GAAI4O,GAAiBI,EAAWjD,YAC9B,MAAO,CAAEhQ,OAAMiT,cAIjB,IAAIhT,EAAaT,EAAUQ,GAS3B,OALIiS,GAAahS,EAAWX,QAAUwS,GAAmB7R,EAAWX,SAClEiT,EAAac,OAAO,QAAS,IAE/BpT,EAAWX,OAAM,IAAOiT,EAEjB,CAAEvS,KAAMH,EAAWI,GAAagT,aACzC,CAIA,SAASK,GACP/N,EACAgO,GAEA,IAAIC,EAAkBjO,EACtB,GAAIgO,EAAY,CACd,IAAI3U,EAAQ2G,EAAQkO,WAAWC,GAAMA,EAAElQ,MAAMG,KAAO4P,IAChD3U,GAAS,IACX4U,EAAkBjO,EAAQR,MAAM,EAAGnG,GAEvC,CACA,OAAO4U,CACT,CAEA,SAASG,GACP5S,EACAjC,EACAyG,EACA0N,EACAtU,EACAiV,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAhQ,EACAiQ,GAEA,IAAIC,EAAeD,EACfE,GAAcF,EAAoB,IAChCA,EAAoB,GAAG7R,MACvB6R,EAAoB,GAAGtO,UACzB9B,EACAuQ,EAAazT,EAAQQ,UAAUzC,EAAMH,UACrC8V,EAAU1T,EAAQQ,UAAU5C,GAG5B4U,EACFc,GAAuBE,GAAcF,EAAoB,IACrDA,EAAoB,QACpBpQ,EACFuP,EAAkBD,EAClBD,GAA8B/N,EAASgO,GACvChO,EAKAmP,EAAeL,EACfA,EAAoB,GAAGM,gBACvB1Q,EACA2Q,EACFf,GAA+Ba,GAAgBA,GAAgB,IAE7DG,EAAoBrB,EAAgB3L,QAAO,CAACjC,EAAOhH,KACrD,IAAI4E,MAAEA,GAAUoC,EAChB,GAAIpC,EAAMsR,KAER,OAAO,EAGT,GAAoB,MAAhBtR,EAAMuR,OACR,OAAO,EAGT,GAAInB,EACF,QAA4B,mBAAjBpQ,EAAMuR,SAAyBvR,EAAMuR,OAAOC,eAItB/Q,IAA/BnF,EAAM+G,WAAWrC,EAAMG,OAErB7E,EAAMmW,aAAqChR,IAA3BnF,EAAMmW,OAAOzR,EAAMG,KAKzC,GA0HJ,SACEuR,EACAC,EACAvP,GAEA,IAAIwP,GAEDD,GAEDvP,EAAMpC,MAAMG,KAAOwR,EAAa3R,MAAMG,GAIpC0R,OAAsDpR,IAAtCiR,EAAkBtP,EAAMpC,MAAMG,IAGlD,OAAOyR,GAASC,CAClB,CA1IMC,CAAYxW,EAAM+G,WAAY/G,EAAMyG,QAAQ3G,GAAQgH,IACpDmO,EAAwBnM,MAAMjE,GAAOA,IAAOiC,EAAMpC,MAAMG,KAExD,OAAO,EAOT,IAAI4R,EAAoBzW,EAAMyG,QAAQ3G,GAClC4W,EAAiB5P,EAErB,OAAO6P,GAAuB7P,EAAKxG,EAAA,CACjCoV,aACAkB,cAAeH,EAAkBzP,OACjC2O,UACAkB,WAAYH,EAAe1P,QACxBmN,EAAU,CACbqB,eACAsB,sBAAuBlB,EACvBmB,yBAAyBjB,IAGrBd,GACAU,EAAWnV,SAAWmV,EAAWlV,SAC/BmV,EAAQpV,SAAWoV,EAAQnV,QAE7BkV,EAAWlV,SAAWmV,EAAQnV,QAC9BwW,GAAmBP,EAAmBC,MAC1C,IAIAO,EAA8C,GAoFlD,OAnFA7B,EAAiBzN,SAAQ,CAACuP,EAAGjX,KAM3B,GACE6U,IACCrO,EAAQqC,MAAM8L,GAAMA,EAAElQ,MAAMG,KAAOqS,EAAEC,WACtChC,EAAgBiC,IAAInX,GAEpB,OAGF,IAAIoX,EAAiBjS,EAAYkQ,EAAa4B,EAAEhW,KAAMoE,GAMtD,IAAK+R,EASH,YARAJ,EAAqB3T,KAAK,CACxBrD,MACAkX,QAASD,EAAEC,QACXjW,KAAMgW,EAAEhW,KACRuF,QAAS,KACTK,MAAO,KACP0G,WAAY,OAQhB,IAAI8J,EAAUtX,EAAMuX,SAAS7I,IAAIzO,GAC7BuX,EAAeC,GAAeJ,EAAgBH,EAAEhW,MAEhDwW,GAAmB,EAGrBA,GAFErC,EAAiB+B,IAAInX,OAGdiV,EAAsBrN,SAAS5H,KAIxCqX,GACkB,SAAlBA,EAAQtX,YACSmF,IAAjBmS,EAAQrQ,KAKW+N,EAIA2B,GAAuBa,EAAYlX,EAAA,CACpDoV,aACAkB,cAAe5W,EAAMyG,QAAQzG,EAAMyG,QAAQT,OAAS,GAAGgB,OACvD2O,UACAkB,WAAYpQ,EAAQA,EAAQT,OAAS,GAAGgB,QACrCmN,EAAU,CACbqB,eACAsB,sBAAuBlB,EACvBmB,yBAAyBjB,GAErBd,OAIJ0C,GACFT,EAAqB3T,KAAK,CACxBrD,MACAkX,QAASD,EAAEC,QACXjW,KAAMgW,EAAEhW,KACRuF,QAAS4Q,EACTvQ,MAAO0Q,EACPhK,WAAY,IAAIC,iBAEpB,IAGK,CAACsI,EAAmBkB,EAC7B,CAqBA,SAASD,GACPX,EACAvP,GAEA,IAAI6Q,EAActB,EAAa3R,MAAMxD,KACrC,OAEEmV,EAAa9V,WAAauG,EAAMvG,UAGhB,MAAfoX,GACCA,EAAYtP,SAAS,MACrBgO,EAAarP,OAAO,OAASF,EAAME,OAAO,IAEhD,CAEA,SAAS2P,GACPiB,EACAC,GAEA,GAAID,EAAYlT,MAAMgT,iBAAkB,CACtC,IAAII,EAAcF,EAAYlT,MAAMgT,iBAAiBG,GACrD,GAA2B,kBAAhBC,EACT,OAAOA,CAEX,CAEA,OAAOD,EAAId,uBACb,CAMA1H,eAAe0I,GACbC,EACA9W,EACAuF,EACApC,EACAG,EACAF,EACA2T,EACArK,GAEA,IAAI3N,EAAM,CAACiB,KAASuF,EAAQhC,KAAKmQ,GAAMA,EAAElQ,MAAMG,MAAKC,KAAK,KACzD,IACE,IAAIoT,EAAUD,EAAqBvJ,IAAIzO,GAClCiY,IACHA,EAAUF,EAAsB,CAC9B9W,OACAuF,UACA0R,MAAOA,CAAChB,EAASpS,KACV6I,EAAOe,SACVyJ,GACEjB,EACApS,EACAV,EACAG,EACAF,EAEJ,IAGJ2T,EAAqB5H,IAAIpQ,EAAKiY,IAG5BA,IAu2BgB,iBADQG,EAt2BoBH,IAu2BT,MAAPG,GAAe,SAAUA,UAt2BjDH,CAIV,CAFU,QACRD,EAAqBrJ,OAAO3O,EAC9B,CAi2BF,IAAgCoY,CAh2BhC,CAEA,SAASD,GACPjB,EACApS,EACAuQ,EACA9Q,EACAF,GAEA,GAAI6S,EAAS,CAAA,IAAAmB,EACX,IAAI5T,EAAQF,EAAS2S,GACrBhY,EACEuF,EACoDyS,oDAAAA,GAEtD,IAAIoB,EAAenU,EACjBW,EACAT,EACA,CAAC6S,EAAS,QAASvS,eAAO0T,EAAA5T,EAAMK,iBAANuT,EAAgBtS,SAAU,MACpDxB,GAEEE,EAAMK,SACRL,EAAMK,SAASzB,QAAQiV,GAEvB7T,EAAMK,SAAWwT,CAErB,KAAO,CACL,IAAIA,EAAenU,EACjBW,EACAT,EACA,CAAC,QAASM,OAAO0Q,EAAYtP,QAAU,MACvCxB,GAEF8Q,EAAYhS,QAAQiV,EACtB,CACF,CAOAlJ,eAAemJ,GACb9T,EACAJ,EACAE,GAEA,IAAKE,EAAMsR,KACT,OAGF,IAAIyC,QAAkB/T,EAAMsR,OAK5B,IAAKtR,EAAMsR,KACT,OAGF,IAAI0C,EAAgBlU,EAASE,EAAMG,IACnC1F,EAAUuZ,EAAe,8BAUzB,IAAIC,EAAoC,CAAA,EACxC,IAAK,IAAIC,KAAqBH,EAAW,CACvC,IAGII,OACmB1T,IAHrBuT,EAAcE,IAMQ,qBAAtBA,EAEFrZ,GACGsZ,EACD,UAAUH,EAAc7T,GAAE,4BAA4B+T,EAAtD,yGAE8BA,wBAI7BC,GACA3U,EAAmBkT,IAAIwB,KAExBD,EAAaC,GACXH,EAAUG,GAEhB,CAIAnP,OAAO5F,OAAO6U,EAAeC,GAK7BlP,OAAO5F,OAAO6U,EAAapY,EAKtBgE,CAAAA,EAAAA,EAAmBoU,GAAc,CACpC1C,UAAM7Q,IAEV,CAGA,SAAS2T,GACP1F,GAEA,OAAO9F,QAAQyL,IAAI3F,EAAK3M,QAAQhC,KAAKmQ,GAAMA,EAAEtF,YAC/C,CAEAD,eAAe2J,GACbC,EACAtF,EACAvB,EACA8G,EACAzS,EACAjC,EACAF,EACA6U,GAEA,IAAIC,EAAiBF,EAAclQ,QACjC,CAAC8E,EAAK8G,IAAM9G,EAAIK,IAAIyG,EAAElQ,MAAMG,KAC5B,IAAIV,KAEFkV,EAAgB,IAAIlV,IAKpBmV,QAAgBL,EAAiB,CACnCxS,QAASA,EAAQhC,KAAKqC,IACpB,IAAIyS,EAAaH,EAAehC,IAAItQ,EAAMpC,MAAMG,IAoBhD,OAAAvE,KACKwG,EAAK,CACRyS,aACAjK,QAlB2CkK,IAC3CH,EAAclL,IAAIrH,EAAMpC,MAAMG,IACvB0U,EAwCflK,eACEsE,EACAvB,EACAtL,EACAtC,EACAF,EACAkV,EACAC,GAEA,IAAIjR,EACAkR,EAEAC,EACFC,IAGA,IAAI9M,EAGAO,EAAe,IAAIC,SAAuB,CAACtD,EAAGuD,IAAOT,EAASS,IAClEmM,EAAWA,IAAM5M,IACjBsF,EAAQxE,OAAO1K,iBAAiB,QAASwW,GAEzC,IAmBIG,EAnBAC,EAAiBC,GACI,mBAAZH,EACFtM,QAAQR,OACb,IAAIxN,MACF,oEACMqU,EAAI,eAAe7M,EAAMpC,MAAMG,GAAE,MAItC+U,EACL,CACExH,UACApL,OAAQF,EAAME,OACdgT,QAASP,WAECtU,IAAR4U,EAAoB,CAACA,GAAO,IAkBpC,OAZEF,EADEL,EACeA,GAAiBO,GAAiBD,EAAcC,KAEhD,WACf,IAEE,MAAO,CAAEpG,KAAM,OAAQnL,aADPsR,IAIlB,CAFE,MAAOna,GACP,MAAO,CAAEgU,KAAM,QAASnL,OAAQ7I,EAClC,CACD,EAPgB,GAUZ2N,QAAQe,KAAK,CAACwL,EAAgBxM,GAAc,EAGrD,IACE,IAAIuM,EAAU9S,EAAMpC,MAAMiP,GAE1B,GAAI7M,EAAMpC,MAAMsR,KACd,GAAI4D,EAAS,CAEX,IAAIK,GACC7a,SAAekO,QAAQyL,IAAI,CAI9BY,EAAWC,GAASpL,OAAO7O,IACzBsa,EAAeta,CAAC,IAElB6Y,GAAoB1R,EAAMpC,MAAOJ,EAAoBE,KAEvD,QAAqBW,IAAjB8U,EACF,MAAMA,EAERzR,EAASpJ,CACX,KAAO,CAKL,SAHMoZ,GAAoB1R,EAAMpC,MAAOJ,EAAoBE,GAE3DoV,EAAU9S,EAAMpC,MAAMiP,IAClBiG,EAKG,IAAa,WAATjG,EAAmB,CAC5B,IAAItQ,EAAM,IAAIP,IAAIsP,EAAQ/O,KACtB9C,EAAW8C,EAAI9C,SAAW8C,EAAI7C,OAClC,MAAMgT,GAAuB,IAAK,CAChCf,OAAQL,EAAQK,OAChBlS,WACA4W,QAASrQ,EAAMpC,MAAMG,IAEzB,CAGE,MAAO,CAAE8O,KAAM1P,EAAWgD,KAAMuB,YAAQrD,EAC1C,CAbEqD,QAAemR,EAAWC,EAc9B,KACK,KAAKA,EAAS,CACnB,IAAIvW,EAAM,IAAIP,IAAIsP,EAAQ/O,KAE1B,MAAMmQ,GAAuB,IAAK,CAChCjT,SAFa8C,EAAI9C,SAAW8C,EAAI7C,QAIpC,CACEgI,QAAemR,EAAWC,EAC5B,CAEAza,OACoBgG,IAAlBqD,EAAOA,OACP,gBAAwB,WAATmL,EAAoB,YAAc,YAAjD,eACM7M,EAAMpC,MAAMG,GAA8C8O,4CAAAA,EADhE,+CAaJ,CATE,MAAOhU,GAIP,MAAO,CAAEgU,KAAM1P,EAAWP,MAAO8E,OAAQ7I,EAC3C,CAAU,QACJ+Z,GACFtH,EAAQxE,OAAOzK,oBAAoB,QAASuW,EAEhD,CAEA,OAAOlR,CACT,CA1KY0R,CACEvG,EACAvB,EACAtL,EACAtC,EACAF,EACAkV,EACAL,GAEF7L,QAAQgC,QAAQ,CAAEqE,KAAM1P,EAAWgD,KAAMuB,YAAQrD,MAM9C,IAGXiN,UACApL,OAAQP,EAAQ,GAAGO,OACnBgT,QAASb,IAeX,OAVA1S,EAAQkB,SAASiN,GACfzV,EACEka,EAAcjC,IAAIxC,EAAElQ,MAAMG,IAC1B,kDAAoD+P,EAAElQ,MAAMG,GAA5D,0HAOGyU,EAAQvQ,QAAO,CAACiB,EAAG7D,IAAMiT,EAAehC,IAAI3Q,EAAQN,GAAGzB,MAAMG,KACtE,CAwIAwK,eAAe8K,GACbC,GAEA,IAAI5R,OAAEA,EAAMmL,KAAEA,EAAIzD,OAAEA,GAAWkK,EAE/B,GAAIC,GAAW7R,GAAS,CACtB,IAAIvB,EAEJ,IACE,IAAIqT,EAAc9R,EAAO2H,QAAQzB,IAAI,gBAKjCzH,EAFAqT,GAAe,wBAAwBpR,KAAKoR,GAC3B,MAAf9R,EAAO6K,KACF,WAEM7K,EAAO8I,aAGT9I,EAAO+I,MAIxB,CAFE,MAAO5R,GACP,MAAO,CAAEgU,KAAM1P,EAAWP,MAAOA,MAAO/D,EAC1C,CAEA,OAAIgU,IAAS1P,EAAWP,MACf,CACLiQ,KAAM1P,EAAWP,MACjBA,MAAO,IAAI6M,EAAkB/H,EAAO0H,OAAQ1H,EAAOgI,WAAYvJ,GAC/D4O,WAAYrN,EAAO0H,OACnBC,QAAS3H,EAAO2H,SAIb,CACLwD,KAAM1P,EAAWgD,KACjBA,OACA4O,WAAYrN,EAAO0H,OACnBC,QAAS3H,EAAO2H,QAEpB,CAEA,OAAIwD,IAAS1P,EAAWP,MACf,CACLiQ,KAAM1P,EAAWP,MACjBA,MAAO8E,EACPqN,WAAYnF,EAAqBlI,GAAUA,EAAO0H,OAASA,GAI3DqK,GAAe/R,GACV,CACLmL,KAAM1P,EAAWuW,SACjBC,aAAcjS,EACdqN,WAAuB,OAAb6E,EAAElS,EAAO0F,WAAI,EAAXwM,EAAaxK,OACzBC,SAASwK,OAAAA,EAAAnS,EAAO0F,WAAPyM,EAAAA,EAAaxK,UAAW,IAAIC,QAAQ5H,EAAO0F,KAAKiC,UAItD,CAAEwD,KAAM1P,EAAWgD,KAAMA,KAAMuB,EAAQqN,WAAY3F,GAT9B,IAAAwK,EAAAC,CAU9B,CAGA,SAASC,GACPC,EACAzI,EACA+E,EACA1Q,EACAnB,EACAqG,GAEA,IAAI9L,EAAWgb,EAAS1K,QAAQzB,IAAI,YAMpC,GALAvP,EACEU,EACA,+EAGG+R,EAAmB1I,KAAKrJ,GAAW,CACtC,IAAIib,EAAiBrU,EAAQR,MAC3B,EACAQ,EAAQkO,WAAWC,GAAMA,EAAElQ,MAAMG,KAAOsS,IAAW,GAErDtX,EAAW6S,GACT,IAAI5P,IAAIsP,EAAQ/O,KAChByX,EACAxV,GACA,EACAzF,EACA8L,GAEFkP,EAAS1K,QAAQE,IAAI,WAAYxQ,EACnC,CAEA,OAAOgb,CACT,CAEA,SAASE,GACPlb,EACA6V,EACApQ,GAEA,GAAIsM,EAAmB1I,KAAKrJ,GAAW,CAErC,IAAImb,EAAqBnb,EACrBwD,EAAM2X,EAAmBzT,WAAW,MACpC,IAAIzE,IAAI4S,EAAWuF,SAAWD,GAC9B,IAAIlY,IAAIkY,GACRE,EAA0D,MAAzCzV,EAAcpC,EAAI9C,SAAU+E,GACjD,GAAIjC,EAAIV,SAAW+S,EAAW/S,QAAUuY,EACtC,OAAO7X,EAAI9C,SAAW8C,EAAI7C,OAAS6C,EAAI5C,IAE3C,CACA,OAAOZ,CACT,CAKA,SAASsb,GACPlZ,EACApC,EACA+N,EACAuG,GAEA,IAAI9Q,EAAMpB,EAAQQ,UAAUqR,GAAkBjU,IAAWgB,WACrDqN,EAAoB,CAAEN,UAE1B,GAAIuG,GAAcJ,GAAiBI,EAAWjD,YAAa,CACzD,IAAIA,WAAEA,EAAUE,YAAEA,GAAgB+C,EAIlCjG,EAAKuE,OAASvB,EAAW2C,cAEL,qBAAhBzC,GACFlD,EAAKiC,QAAU,IAAIC,QAAQ,CAAE,eAAgBgB,IAC7ClD,EAAKmF,KAAO9H,KAAKC,UAAU2I,EAAW7C,OACb,eAAhBF,EAETlD,EAAKmF,KAAOc,EAAW5C,KAEP,sCAAhBH,GACA+C,EAAW9C,SAGXnD,EAAKmF,KAAOgB,GAA8BF,EAAW9C,UAGrDnD,EAAKmF,KAAOc,EAAW9C,QAE3B,CAEA,OAAO,IAAI+J,QAAQ/X,EAAK6K,EAC1B,CAEA,SAASmG,GAA8BhD,GACrC,IAAIoC,EAAe,IAAIQ,gBAEvB,IAAK,IAAKhU,EAAKb,KAAUiS,EAASxD,UAEhC4F,EAAac,OAAOtU,EAAsB,iBAAVb,EAAqBA,EAAQA,EAAMwE,MAGrE,OAAO6P,CACT,CAEA,SAASa,GACPb,GAEA,IAAIpC,EAAW,IAAI2C,SACnB,IAAK,IAAK/T,EAAKb,KAAUqU,EAAa5F,UACpCwD,EAASkD,OAAOtU,EAAKb,GAEvB,OAAOiS,CACT,CAEA,SAASgK,GACP5U,EACAyS,EACAI,EACA/D,EACA+F,EACAC,GAQA,IAEI1F,EAFA9O,EAAwC,CAAA,EACxCoP,EAAuC,KAEvCqF,GAAa,EACbC,EAAyC,CAAA,EACzCC,EACFnG,GAAuBE,GAAcF,EAAoB,IACrDA,EAAoB,GAAG7R,WACvByB,EAqFN,OAlFAmU,EAAQ3R,SAAQ,CAACa,EAAQ1I,KACvB,IAAI+E,EAAKqU,EAAcpZ,GAAO4E,MAAMG,GAKpC,GAJA1F,GACGwc,GAAiBnT,GAClB,uDAEEiN,GAAcjN,GAAS,CACzB,IAAI9E,EAAQ8E,EAAO9E,MAWnB,QAPqByB,IAAjBuW,IACFhY,EAAQgY,EACRA,OAAevW,GAGjBgR,EAASA,GAAU,GAEfoF,EACFpF,EAAOtR,GAAMnB,MACR,CAIL,IAAIkY,EAAgBC,GAAoBpV,EAAS5B,GACX,MAAlCsR,EAAOyF,EAAclX,MAAMG,MAC7BsR,EAAOyF,EAAclX,MAAMG,IAAMnB,EAErC,CAGAqD,EAAWlC,QAAMM,EAIZqW,IACHA,GAAa,EACb3F,EAAanF,EAAqBlI,EAAO9E,OACrC8E,EAAO9E,MAAMwM,OACb,KAEF1H,EAAO2H,UACTsL,EAAc5W,GAAM2D,EAAO2H,QAE/B,MACM2L,GAAiBtT,IACnB8S,EAAgBjL,IAAIxL,EAAI2D,EAAOiS,cAC/B1T,EAAWlC,GAAM2D,EAAOiS,aAAaxT,KAId,MAArBuB,EAAOqN,YACe,MAAtBrN,EAAOqN,YACN2F,IAED3F,EAAarN,EAAOqN,YAElBrN,EAAO2H,UACTsL,EAAc5W,GAAM2D,EAAO2H,WAG7BpJ,EAAWlC,GAAM2D,EAAOvB,KAGpBuB,EAAOqN,YAAoC,MAAtBrN,EAAOqN,aAAuB2F,IACrD3F,EAAarN,EAAOqN,YAElBrN,EAAO2H,UACTsL,EAAc5W,GAAM2D,EAAO2H,SAGjC,SAMmBhL,IAAjBuW,GAA8BnG,IAChCY,EAAS,CAAE,CAACZ,EAAoB,IAAKmG,GACrC3U,EAAWwO,EAAoB,SAAMpQ,GAGhC,CACL4B,aACAoP,SACAN,WAAYA,GAAc,IAC1B4F,gBAEJ,CAEA,SAASM,GACP/b,EACAyG,EACAyS,EACAI,EACA/D,EACA0B,EACA+E,EACAV,GAKA,IAAIvU,WAAEA,EAAUoP,OAAEA,GAAWkF,GAC3B5U,EACAyS,EACAI,EACA/D,EACA+F,GACA,GAIF,IAAK,IAAIxb,EAAQ,EAAGA,EAAQmX,EAAqBjR,OAAQlG,IAAS,CAChE,IAAIG,IAAEA,EAAG6G,MAAEA,EAAK0G,WAAEA,GAAeyJ,EAAqBnX,GACtDX,OACqBgG,IAAnB6W,QAA0D7W,IAA1B6W,EAAelc,GAC/C,6CAEF,IAAI0I,EAASwT,EAAelc,GAG5B,IAAI0N,IAAcA,EAAWI,OAAOe,QAG7B,GAAI8G,GAAcjN,GAAS,CAChC,IAAIoT,EAAgBC,GAAoB7b,EAAMyG,cAASK,SAAAA,EAAOpC,MAAMG,IAC9DsR,GAAUA,EAAOyF,EAAclX,MAAMG,MACzCsR,EAAM7V,EAAA,CAAA,EACD6V,EAAM,CACT,CAACyF,EAAclX,MAAMG,IAAK2D,EAAO9E,SAGrC1D,EAAMuX,SAAS3I,OAAO3O,EACxB,MAAO,GAAI0b,GAAiBnT,GAG1BrJ,GAAU,EAAO,gDACZ,GAAI2c,GAAiBtT,GAG1BrJ,GAAU,EAAO,uCACZ,CACL,IAAI8c,EAAcC,GAAe1T,EAAOvB,MACxCjH,EAAMuX,SAASlH,IAAIpQ,EAAKgc,EAC1B,CACF,CAEA,MAAO,CAAElV,aAAYoP,SACvB,CAEA,SAASgG,GACPpV,EACAqV,EACA3V,EACA0P,GAEA,IAAIkG,EAAgB/b,EAAA,CAAA,EAAQ8b,GAC5B,IAAK,IAAItV,KAASL,EAAS,CACzB,IAAI5B,EAAKiC,EAAMpC,MAAMG,GAerB,GAdIuX,EAAcE,eAAezX,QACLM,IAAtBiX,EAAcvX,KAChBwX,EAAiBxX,GAAMuX,EAAcvX,SAMXM,IAAnB4B,EAAWlC,IAAqBiC,EAAMpC,MAAMuR,SAGrDoG,EAAiBxX,GAAMkC,EAAWlC,IAGhCsR,GAAUA,EAAOmG,eAAezX,GAElC,KAEJ,CACA,OAAOwX,CACT,CAEA,SAASE,GACPhH,GAEA,OAAKA,EAGEE,GAAcF,EAAoB,IACrC,CAEEiH,WAAY,CAAC,GAEf,CACEA,WAAY,CACV,CAACjH,EAAoB,IAAKA,EAAoB,GAAGtO,OAThD,EAYX,CAKA,SAAS4U,GACPpV,EACA0Q,GAKA,OAHsBA,EAClB1Q,EAAQR,MAAM,EAAGQ,EAAQkO,WAAWC,GAAMA,EAAElQ,MAAMG,KAAOsS,IAAW,GACpE,IAAI1Q,IAEUgW,UAAUC,MAAM9H,IAAmC,IAA7BA,EAAElQ,MAAMoN,oBAC9CrL,EAAQ,EAEZ,CAEA,SAASkW,GAAuBtY,GAK9B,IAAIK,EACgB,IAAlBL,EAAO2B,OACH3B,EAAO,GACPA,EAAOqY,MAAMnP,GAAMA,EAAEzN,QAAUyN,EAAErM,MAAmB,MAAXqM,EAAErM,QAAiB,CAC1D2D,GAAE,wBAGV,MAAO,CACL4B,QAAS,CACP,CACEO,OAAQ,CAAE,EACVzG,SAAU,GACVmJ,aAAc,GACdhF,UAGJA,QAEJ,CAEA,SAAS8O,GACPtD,EAAc0M,GAcd,IAbArc,SACEA,EAAQ4W,QACRA,EAAO1E,OACPA,EAAMkB,KACNA,EAAItU,QACJA,QAOD,IAAAud,EAAG,CAAA,EAAEA,EAEFpM,EAAa,uBACbqM,EAAe,kCAoCnB,OAlCe,MAAX3M,GACFM,EAAa,cACA,oBAATmD,EACFkJ,EACE,wBAAwBtc,EAAxB,4CACW4W,EAAO,iCAAkC9X,EAC7CoT,GAAUlS,GAAY4W,EAC/B0F,EACE,cAAcpK,EAAM,gBAAgBlS,EAApC,+CAC2C4W,EAD3C,+CAGgB,iBAATxD,EACTkJ,EAAe,sCACG,iBAATlJ,IACTkJ,EAAe,qCAEG,MAAX3M,GACTM,EAAa,YACbqM,EAAyB1F,UAAAA,EAAgC5W,yBAAAA,EAAW,KAChD,MAAX2P,GACTM,EAAa,YACbqM,EAAY,yBAA4Btc,EAAW,KAC/B,MAAX2P,IACTM,EAAa,qBACTiC,GAAUlS,GAAY4W,EACxB0F,EACE,cAAcpK,EAAOoB,cAAa,gBAAgBtT,EAAlD,gDAC4C4W,EAD5C,+CAGO1E,IACToK,6BAA0CpK,EAAOoB,cAAgB,MAI9D,IAAItD,EACTL,GAAU,IACVM,EACA,IAAIlR,MAAMud,IACV,EAEJ,CAGA,SAASC,GACPxD,GAEA,IAAK,IAAInT,EAAImT,EAAQtT,OAAS,EAAGG,GAAK,EAAGA,IAAK,CAC5C,IAAIqC,EAAS8Q,EAAQnT,GACrB,GAAIwV,GAAiBnT,GACnB,MAAO,CAAEA,SAAQtI,IAAKiG,EAE1B,CACF,CAEA,SAAS2N,GAAkB5S,GAEzB,OAAOH,EAAUT,EAAA,CAAA,EADgB,iBAATY,EAAoBR,EAAUQ,GAAQA,EAC7B,CAAET,KAAM,KAC3C,CAqCA,SAASsc,GAAwBvU,GAC/B,OACE6R,GAAW7R,EAAOA,SAAWuI,EAAoBqG,IAAI5O,EAAOA,OAAO0H,OAEvE,CAEA,SAAS4L,GAAiBtT,GACxB,OAAOA,EAAOmL,OAAS1P,EAAWuW,QACpC,CAEA,SAAS/E,GAAcjN,GACrB,OAAOA,EAAOmL,OAAS1P,EAAWP,KACpC,CAEA,SAASiY,GAAiBnT,GACxB,OAAQA,GAAUA,EAAOmL,QAAU1P,EAAWgM,QAChD,CAEO,SAASsK,GAAenb,GAC7B,IAAIob,EAAyBpb,EAC7B,OACEob,GACoB,iBAAbA,GACkB,iBAAlBA,EAASvT,MACc,mBAAvBuT,EAASvL,WACW,mBAApBuL,EAAStL,QACgB,mBAAzBsL,EAASwC,WAEpB,CAEA,SAAS3C,GAAWjb,GAClB,OACW,MAATA,GACwB,iBAAjBA,EAAM8Q,QACe,iBAArB9Q,EAAMoR,YACY,iBAAlBpR,EAAM+Q,cACS,IAAf/Q,EAAMiU,IAEjB,CAYA,SAASE,GAAcd,GACrB,OAAO3B,EAAoBsG,IAAI3E,EAAOhI,cACxC,CAEA,SAASsJ,GACPtB,GAEA,OAAO7B,EAAqBwG,IAAI3E,EAAOhI,cACzC,CAEA4E,eAAe4N,GACbC,EACAhE,EACAI,EACA6D,EACAhK,EACAiD,GAEA,IAAK,IAAItW,EAAQ,EAAGA,EAAQwZ,EAAQtT,OAAQlG,IAAS,CACnD,IAAI0I,EAAS8Q,EAAQxZ,GACjBgH,EAAQoS,EAAcpZ,GAI1B,IAAKgH,EACH,SAGF,IAAIuP,EAAe6G,EAAeR,MAC/B9H,GAAMA,EAAElQ,MAAMG,KAAOiC,EAAOpC,MAAMG,KAEjCuY,EACc,MAAhB/G,IACCW,GAAmBX,EAAcvP,SAC2B3B,KAA5DiR,GAAqBA,EAAkBtP,EAAMpC,MAAMG,KAEtD,GAAIiX,GAAiBtT,KAAY2K,GAAaiK,GAAuB,CAInE,IAAIxP,EAASuP,EAAQrd,GACrBX,EACEyO,EACA,0EAEIyP,GAAoB7U,EAAQoF,EAAQuF,GAAW7E,MAAM9F,IACrDA,IACF8Q,EAAQxZ,GAAS0I,GAAU8Q,EAAQxZ,GACrC,GAEJ,CACF,CACF,CAEAuP,eAAegO,GACb7U,EACAoF,EACA0P,GAGA,QAHM,IAANA,IAAAA,GAAS,UAEW9U,EAAOiS,aAAauC,YAAYpP,GACpD,CAIA,GAAI0P,EACF,IACE,MAAO,CACL3J,KAAM1P,EAAWgD,KACjBA,KAAMuB,EAAOiS,aAAajL,cAQ9B,CANE,MAAO7P,GAEP,MAAO,CACLgU,KAAM1P,EAAWP,MACjBA,MAAO/D,EAEX,CAGF,MAAO,CACLgU,KAAM1P,EAAWgD,KACjBA,KAAMuB,EAAOiS,aAAaxT,KAnB5B,CAqBF,CAEA,SAAS+L,GAAmBxS,GAC1B,OAAO,IAAIyT,gBAAgBzT,GAAQ+c,OAAO,SAASzU,MAAMyB,GAAY,KAANA,GACjE,CAEA,SAASkN,GACPhR,EACA5G,GAEA,IAAIW,EACkB,iBAAbX,EAAwBa,EAAUb,GAAUW,OAASX,EAASW,OACvE,GACEiG,EAAQA,EAAQT,OAAS,GAAGtB,MAAM5E,OAClCkT,GAAmBxS,GAAU,IAG7B,OAAOiG,EAAQA,EAAQT,OAAS,GAIlC,IAAI4F,EAAcH,EAA2BhF,GAC7C,OAAOmF,EAAYA,EAAY5F,OAAS,EAC1C,CAEA,SAASwX,GACPC,GAEA,IAAIvM,WAAEA,EAAUC,WAAEA,EAAUC,YAAEA,EAAWG,KAAEA,EAAIF,SAAEA,EAAQC,KAAEA,GACzDmM,EACF,GAAKvM,GAAeC,GAAeC,EAInC,OAAY,MAARG,EACK,CACLL,aACAC,aACAC,cACAC,cAAUlM,EACVmM,UAAMnM,EACNoM,QAEmB,MAAZF,EACF,CACLH,aACAC,aACAC,cACAC,WACAC,UAAMnM,EACNoM,UAAMpM,QAEUA,IAATmM,EACF,CACLJ,aACAC,aACAC,cACAC,cAAUlM,EACVmM,OACAC,UAAMpM,QAPH,CAUT,CAEA,SAASuY,GACP7d,EACAsU,GAEA,GAAIA,EAAY,CAWd,MAV8C,CAC5CnU,MAAO,UACPH,WACAqR,WAAYiD,EAAWjD,WACvBC,WAAYgD,EAAWhD,WACvBC,YAAa+C,EAAW/C,YACxBC,SAAU8C,EAAW9C,SACrBC,KAAM6C,EAAW7C,KACjBC,KAAM4C,EAAW5C,KAGrB,CAWE,MAV8C,CAC5CvR,MAAO,UACPH,WACAqR,gBAAY/L,EACZgM,gBAAYhM,EACZiM,iBAAajM,EACbkM,cAAUlM,EACVmM,UAAMnM,EACNoM,UAAMpM,EAIZ,CAEA,SAASwY,GACP9d,EACAsU,GAYA,MAViD,CAC/CnU,MAAO,aACPH,WACAqR,WAAYiD,EAAWjD,WACvBC,WAAYgD,EAAWhD,WACvBC,YAAa+C,EAAW/C,YACxBC,SAAU8C,EAAW9C,SACrBC,KAAM6C,EAAW7C,KACjBC,KAAM4C,EAAW5C,KAGrB,CAEA,SAASqM,GACPzJ,EACAlN,GAEA,GAAIkN,EAAY,CAWd,MAVwC,CACtCnU,MAAO,UACPkR,WAAYiD,EAAWjD,WACvBC,WAAYgD,EAAWhD,WACvBC,YAAa+C,EAAW/C,YACxBC,SAAU8C,EAAW9C,SACrBC,KAAM6C,EAAW7C,KACjBC,KAAM4C,EAAW5C,KACjBtK,OAGJ,CAWE,MAVwC,CACtCjH,MAAO,UACPkR,gBAAY/L,EACZgM,gBAAYhM,EACZiM,iBAAajM,EACbkM,cAAUlM,EACVmM,UAAMnM,EACNoM,UAAMpM,EACN8B,OAIN,CAmBA,SAASiV,GAAejV,GAWtB,MAVqC,CACnCjH,MAAO,OACPkR,gBAAY/L,EACZgM,gBAAYhM,EACZiM,iBAAajM,EACbkM,cAAUlM,EACVmM,UAAMnM,EACNoM,UAAMpM,EACN8B,OAGJ,oVF1xKO,SACLtF,GAoBA,YApB8B,IAA9BA,IAAAA,EAAiC,CAAA,GAoB1BJ,GAlBP,SACEK,EACAI,GAEA,IAAIzB,SAAEA,EAAQC,OAAEA,EAAMC,KAAEA,GAASmB,EAAO/B,SACxC,OAAOM,EACL,GACA,CAAEI,WAAUC,SAAQC,QAEnBuB,EAAchC,OAASgC,EAAchC,MAAMD,KAAQ,KACnDiC,EAAchC,OAASgC,EAAchC,MAAMC,KAAQ,UAExD,IAEA,SAA2B2B,EAAgBvB,GACzC,MAAqB,iBAAPA,EAAkBA,EAAKU,EAAWV,EAClD,GAKE,KACAsB,EAEJ,sBA8BO,SACLA,GAqDA,YArD2B,IAA3BA,IAAAA,EAA8B,CAAA,GAqDvBJ,GAnDP,SACEK,EACAI,GAEA,IAAIzB,SACFA,EAAW,IAAGC,OACdA,EAAS,GAAEC,KACXA,EAAO,IACLC,EAAUkB,EAAO/B,SAASY,KAAKK,OAAO,IAY1C,OAJKP,EAASgH,WAAW,MAAShH,EAASgH,WAAW,OACpDhH,EAAW,IAAMA,GAGZJ,EACL,GACA,CAAEI,WAAUC,SAAQC,QAEnBuB,EAAchC,OAASgC,EAAchC,MAAMD,KAAQ,KACnDiC,EAAchC,OAASgC,EAAchC,MAAMC,KAAQ,UAExD,IAEA,SAAwB2B,EAAgBvB,GACtC,IAAIqC,EAAOd,EAAOC,SAASgc,cAAc,QACrCjb,EAAO,GAEX,GAAIF,GAAQA,EAAKob,aAAa,QAAS,CACrC,IAAIza,EAAMzB,EAAO/B,SAAS+C,KACtBxB,EAAYiC,EAAIhC,QAAQ,KAC5BuB,GAAsB,IAAfxB,EAAmBiC,EAAMA,EAAI4C,MAAM,EAAG7E,EAC/C,CAEA,OAAOwB,EAAO,KAAqB,iBAAPvC,EAAkBA,EAAKU,EAAWV,GAChE,IAEA,SAA8BR,EAAoBQ,GAChDd,EACkC,MAAhCM,EAASU,SAASU,OAAO,GAAU,6DAC0BsK,KAAKC,UAChEnL,OAGN,GAMEsB,EAEJ,wBAvPO,SACLA,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhC,IACIkM,GADAkQ,eAAEA,EAAiB,CAAC,KAAIC,aAAEA,EAAYjc,SAAEA,GAAW,GAAUJ,EAEjEkM,EAAUkQ,EAAetZ,KAAI,CAACwZ,EAAOne,IACnCoe,EACED,EACiB,iBAAVA,EAAqB,KAAOA,EAAMje,MAC/B,IAAVF,EAAc,eAAYqF,KAG9B,IAAIrF,EAAQqe,EACM,MAAhBH,EAAuBnQ,EAAQ7H,OAAS,EAAIgY,GAE1C9b,EAASjD,EAAOkD,IAChBC,EAA4B,KAEhC,SAAS+b,EAAWna,GAClB,OAAOrD,KAAKyd,IAAIzd,KAAK0d,IAAIra,EAAG,GAAI6J,EAAQ7H,OAAS,EACnD,CACA,SAASsY,IACP,OAAOzQ,EAAQ/N,EACjB,CACA,SAASoe,EACP7d,EACAL,EACAC,QADU,IAAVD,IAAAA,EAAa,MAGb,IAAIH,EAAWM,EACb0N,EAAUyQ,IAAqB/d,SAAW,IAC1CF,EACAL,EACAC,GAQF,OANAV,EACkC,MAAhCM,EAASU,SAASU,OAAO,8DACkCsK,KAAKC,UAC9DnL,IAGGR,CACT,CAEA,SAAS4B,EAAWpB,GAClB,MAAqB,iBAAPA,EAAkBA,EAAKU,EAAWV,EAClD,CA0DA,MAxD6B,CACvBP,YACF,OAAOA,CACR,EACGoC,aACF,OAAOA,CACR,EACGrC,eACF,OAAOye,GACR,EACD7c,aACAgB,UAAUpC,GACD,IAAIyC,IAAIrB,EAAWpB,GAAK,oBAEjC+C,eAAe/C,GACb,IAAIa,EAAqB,iBAAPb,EAAkBK,EAAUL,GAAMA,EACpD,MAAO,CACLE,SAAUW,EAAKX,UAAY,GAC3BC,OAAQU,EAAKV,QAAU,GACvBC,KAAMS,EAAKT,MAAQ,GAEtB,EACD6C,KAAKjD,EAAIL,GACPkC,EAASjD,EAAOsE,KAChB,IAAIgb,EAAeL,EAAqB7d,EAAIL,GAC5CF,GAAS,EACT+N,EAAQ2Q,OAAO1e,EAAO+N,EAAQ7H,OAAQuY,GAClCxc,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAU0e,EAAc/b,MAAO,GAErD,EACDK,QAAQxC,EAAIL,GACVkC,EAASjD,EAAO6E,QAChB,IAAIya,EAAeL,EAAqB7d,EAAIL,GAC5C6N,EAAQ/N,GAASye,EACbxc,GAAYK,GACdA,EAAS,CAAEF,SAAQrC,SAAU0e,EAAc/b,MAAO,GAErD,EACDuB,GAAGvB,GACDN,EAASjD,EAAOkD,IAChB,IAAII,EAAY4b,EAAWre,EAAQ0C,GAC/B+b,EAAe1Q,EAAQtL,GAC3BzC,EAAQyC,EACJH,GACFA,EAAS,CAAEF,SAAQrC,SAAU0e,EAAc/b,SAE9C,EACDQ,OAAOC,IACLb,EAAWa,EACJ,KACLb,EAAW,IAAI,GAMvB,gCEuaO,SAAsB8L,GAC3B,MAAMuQ,EAAevQ,EAAKtM,OACtBsM,EAAKtM,OACa,oBAAXA,OACPA,YACAuD,EACEuZ,OACoB,IAAjBD,QAC0B,IAA1BA,EAAa5c,eAC2B,IAAxC4c,EAAa5c,SAAS8c,cACzBC,GAAYF,EAOlB,IAAIpa,EACJ,GANAnF,EACE+O,EAAK7J,OAAO2B,OAAS,EACrB,6DAIEkI,EAAK5J,mBACPA,EAAqB4J,EAAK5J,wBACrB,GAAI4J,EAAK2Q,oBAAqB,CAEnC,IAAIA,EAAsB3Q,EAAK2Q,oBAC/Bva,EAAsBI,IAAW,CAC/BoN,iBAAkB+M,EAAoBna,IAE1C,MACEJ,EAAqBuN,EAIvB,IAQIiN,EA6DAC,EAoDAC,EAzHAxa,EAA0B,CAAA,EAE1Bya,EAAa7a,EACf8J,EAAK7J,OACLC,OACAa,EACAX,GAGEc,EAAW4I,EAAK5I,UAAY,IAC5B2T,EAAmB/K,EAAKgR,uBAAyBpG,GACjDd,EAAwB9J,EAAKiR,2BAG7B7M,EAAoBhS,EAAA,CACtB8e,mBAAmB,EACnBC,wBAAwB,EACxBC,qBAAqB,EACrBC,oBAAoB,EACpB5T,sBAAsB,EACtB6T,sCAAsC,GACnCtR,EAAKoE,QAGNmN,EAAuC,KAEvCxS,EAAc,IAAI9I,IAElBub,EAAsD,KAEtDC,EAAkE,KAElEC,EAAsD,KAOtDC,EAA8C,MAAtB3R,EAAK4R,cAE7BC,EAAiB3a,EAAY6Z,EAAY/Q,EAAKjM,QAAQpC,SAAUyF,GAChE0a,EAAkC,KAEtC,GAAsB,MAAlBD,IAA2B/H,EAAuB,CAGpD,IAAItU,EAAQ8P,GAAuB,IAAK,CACtCjT,SAAU2N,EAAKjM,QAAQpC,SAASU,YAE9BkG,QAAEA,EAAO/B,MAAEA,GAAUiY,GAAuBsC,GAChDc,EAAiBtZ,EACjBuZ,EAAgB,CAAE,CAACtb,EAAMG,IAAKnB,EAChC,CAKA,GAAIqc,GAAkB/H,EAAuB,CAC5BiI,GACbF,EACAd,EACA/Q,EAAKjM,QAAQpC,SAASU,UAEX2f,SACXH,EAAiB,KAErB,CAGA,GAAKA,EAIE,GAAIA,EAAejX,MAAM8L,GAAMA,EAAElQ,MAAMsR,OAG5C+I,GAAc,OACT,GAAKgB,EAAejX,MAAM8L,GAAMA,EAAElQ,MAAMuR,SAGxC,GAAI3D,EAAOgN,oBAAqB,CAIrC,IAAIvY,EAAamH,EAAK4R,cAAgB5R,EAAK4R,cAAc/Y,WAAa,KAClEoP,EAASjI,EAAK4R,cAAgB5R,EAAK4R,cAAc3J,OAAS,KAC1DgK,EAAsBvL,IAEnBA,EAAElQ,MAAMuR,SAKe,mBAAnBrB,EAAElQ,MAAMuR,SACY,IAA3BrB,EAAElQ,MAAMuR,OAAOC,WAMdnP,QAAyC5B,IAA3B4B,EAAW6N,EAAElQ,MAAMG,KACjCsR,QAAiChR,IAAvBgR,EAAOvB,EAAElQ,MAAMG,KAK9B,GAAIsR,EAAQ,CACV,IAAIjW,EAAM6f,EAAepL,WACtBC,QAA8BzP,IAAxBgR,EAAQvB,EAAElQ,MAAMG,MAEzBka,EAAcgB,EAAe9Z,MAAM,EAAG/F,EAAM,GAAGgG,MAAMia,EACvD,MACEpB,EAAcgB,EAAe7Z,MAAMia,EAEvC,MAGEpB,EAAoC,MAAtB7Q,EAAK4R,mBAtCnBf,GAAc,OARdA,GAAc,EACdgB,EAAiB,GAiDnB,IA0BIK,EA1BApgB,EAAqB,CACvBqgB,cAAenS,EAAKjM,QAAQC,OAC5BrC,SAAUqO,EAAKjM,QAAQpC,SACvB4G,QAASsZ,EACThB,cACAtB,WAAYxM,EAEZqP,sBAA6C,MAAtBpS,EAAK4R,eAAgC,KAC5DS,oBAAoB,EACpBC,aAAc,OACdzZ,WAAamH,EAAK4R,eAAiB5R,EAAK4R,cAAc/Y,YAAe,CAAE,EACvEyV,WAAatO,EAAK4R,eAAiB5R,EAAK4R,cAActD,YAAe,KACrErG,OAASjI,EAAK4R,eAAiB5R,EAAK4R,cAAc3J,QAAW6J,EAC7DzI,SAAU,IAAIkJ,IACdC,SAAU,IAAID,KAKZE,EAA+BC,EAAcze,IAI7C0e,GAA4B,EAM5BC,GAA+B,EAG/BC,EAAmD,IAAIN,IAMvDO,EAAmD,KAInDC,GAA8B,EAM9BjM,GAAyB,EAIzBC,EAAoC,GAIpCC,EAAkC,GAGlCgM,EAAmB,IAAIT,IAGvBU,EAAqB,EAKrBC,IAA2B,EAG3BC,GAAiB,IAAIZ,IAGrBpL,GAAmB,IAAIlR,IAGvBiR,GAAmB,IAAIqL,IAGvBa,GAAiB,IAAIb,IAIrBtL,GAAkB,IAAIhR,IAMtBmX,GAAkB,IAAImF,IAItBc,GAAmB,IAAId,IAIvBe,GAAqB,IAAIf,IAOzBgB,IAA0B,EA+G9B,SAASC,GACPC,EACAvO,QAGC,IAHDA,IAAAA,EAGI,CAAA,GAEJpT,EAAKM,EAAA,CAAA,EACAN,EACA2hB,GAKL,IAAIC,EAA8B,GAC9BC,EAAgC,GAEhCvP,EAAO8M,mBACTpf,EAAMuX,SAAS5P,SAAQ,CAAC2P,EAASrX,KACT,SAAlBqX,EAAQtX,QACNmV,GAAgBiC,IAAInX,GAEtB4hB,EAAoBve,KAAKrD,GAIzB2hB,EAAkBte,KAAKrD,GAE3B,IAOJ,IAAIgN,GAAatF,SAASqH,GACxBA,EAAWhP,EAAO,CAChBmV,gBAAiB0M,EACjBC,4BAA6B1O,EAAK2O,mBAClCC,oBAAuC,IAAnB5O,EAAK6O,cAKzB3P,EAAO8M,oBACTwC,EAAkBja,SAAS1H,GAAQD,EAAMuX,SAAS3I,OAAO3O,KACzD4hB,EAAoBla,SAAS1H,GAAQiiB,GAAcjiB,KAEvD,CAOA,SAASkiB,GACPtiB,EACA8hB,EAA0ES,GAEpE,IAAAC,EAAAC,EAAA,IAaF9F,GAdJyF,UAAEA,QAAoC,IAAAG,EAAG,CAAA,EAAEA,EAOvCG,EACkB,MAApBviB,EAAMwc,YACyB,MAA/Bxc,EAAMyd,WAAWvM,YACjB6C,GAAiB/T,EAAMyd,WAAWvM,aACP,YAA3BlR,EAAMyd,WAAWzd,QACe,KAAlB,OAAdqiB,EAAAxiB,EAASG,YAAK,EAAdqiB,EAAgBG,aAKdhG,EAFAmF,EAASnF,WACP/S,OAAOgZ,KAAKd,EAASnF,YAAYxW,OAAS,EAC/B2b,EAASnF,WAGT,KAEN+F,EAEIviB,EAAMwc,WAGN,KAIf,IAAIzV,EAAa4a,EAAS5a,WACtBoV,GACEnc,EAAM+G,WACN4a,EAAS5a,WACT4a,EAASlb,SAAW,GACpBkb,EAASxL,QAEXnW,EAAM+G,WAIN2Z,EAAW1gB,EAAM0gB,SACjBA,EAASnR,KAAO,IAClBmR,EAAW,IAAID,IAAIC,GACnBA,EAAS/Y,SAAQ,CAACqC,EAAGoF,IAAMsR,EAASrQ,IAAIjB,EAAGqC,MAK7C,IAsBIsQ,EAtBAxB,GAC4B,IAA9BM,GACgC,MAA/B7gB,EAAMyd,WAAWvM,YAChB6C,GAAiB/T,EAAMyd,WAAWvM,cACF,KAAhCoR,OAAAA,EAAAziB,EAASG,YAATsiB,EAAAA,EAAgBE,aAqBpB,GAlBI1D,IACFG,EAAaH,EACbA,OAAqB3Z,GAGnB8b,GAEON,IAAkBC,EAAcze,MAEhCwe,IAAkBC,EAAcrd,KACzC2K,EAAKjM,QAAQqB,KAAKzD,EAAUA,EAASG,OAC5B2gB,IAAkBC,EAAc9c,SACzCoK,EAAKjM,QAAQY,QAAQhD,EAAUA,EAASG,QAMtC2gB,IAAkBC,EAAcze,IAAK,CAEvC,IAAIugB,EAAa3B,EAAuBrS,IAAI1O,EAAMH,SAASU,UACvDmiB,GAAcA,EAAWtL,IAAIvX,EAASU,UACxCwhB,EAAqB,CACnBY,gBAAiB3iB,EAAMH,SACvB0e,aAAc1e,GAEPkhB,EAAuB3J,IAAIvX,EAASU,YAG7CwhB,EAAqB,CACnBY,gBAAiB9iB,EACjB0e,aAAcve,EAAMH,UAGzB,MAAM,GAAIihB,EAA8B,CAEvC,IAAI8B,EAAU7B,EAAuBrS,IAAI1O,EAAMH,SAASU,UACpDqiB,EACFA,EAAQzU,IAAItO,EAASU,WAErBqiB,EAAU,IAAIze,IAAY,CAACtE,EAASU,WACpCwgB,EAAuB1Q,IAAIrQ,EAAMH,SAASU,SAAUqiB,IAEtDb,EAAqB,CACnBY,gBAAiB3iB,EAAMH,SACvB0e,aAAc1e,EAElB,CAEA6hB,GAAWphB,EAAA,CAAA,EAEJqhB,EAAQ,CACXnF,aACAzV,aACAsZ,cAAeM,EACf9gB,WACAkf,aAAa,EACbtB,WAAYxM,EACZuP,aAAc,OACdF,sBAAuBuC,GACrBhjB,EACA8hB,EAASlb,SAAWzG,EAAMyG,SAE5B8Z,qBACAG,aAEF,CACEqB,qBACAE,WAAyB,IAAdA,IAKftB,EAAgBC,EAAcze,IAC9B0e,GAA4B,EAC5BC,GAA+B,EAC/BG,GAA8B,EAC9BjM,GAAyB,EACzBC,EAA0B,GAC1BC,EAAwB,EAC1B,CAoJA7F,eAAeyT,GACbzC,EACAxgB,EACAuT,GAgBAgN,GAA+BA,EAA4BjR,QAC3DiR,EAA8B,KAC9BO,EAAgBN,EAChBY,GACoD,KAAjD7N,GAAQA,EAAK2P,gCA8mDlB,SACEljB,EACA4G,GAEA,GAAIiZ,GAAwBE,EAAmB,CAC7C,IAAI3f,EAAM+iB,GAAanjB,EAAU4G,GACjCiZ,EAAqBzf,GAAO2f,GAC9B,CACF,CAlnDEqD,CAAmBjjB,EAAMH,SAAUG,EAAMyG,SACzCoa,GAAkE,KAArCzN,GAAQA,EAAKmN,oBAE1CO,GAAuE,KAAvC1N,GAAQA,EAAK8P,sBAE7C,IAAI5N,EAAcwJ,GAAsBG,EACpCkE,EAAoB/P,GAAQA,EAAKgQ,mBACjC3c,EAAUrB,EAAYkQ,EAAazV,EAAUyF,GAC7C2c,GAAyC,KAA5B7O,GAAQA,EAAK6O,WAE1BoB,EAAWpD,GAAcxZ,EAAS6O,EAAazV,EAASU,UAM5D,GALI8iB,EAASnD,QAAUmD,EAAS5c,UAC9BA,EAAU4c,EAAS5c,UAIhBA,EAAS,CACZ,IAAI/C,MAAEA,EAAK4f,gBAAEA,EAAe5e,MAAEA,GAAU6e,GACtC1jB,EAASU,UAaX,YAXA4hB,GACEtiB,EACA,CACE4G,QAAS6c,EACTvc,WAAY,CAAE,EACdoP,OAAQ,CACN,CAACzR,EAAMG,IAAKnB,IAGhB,CAAEue,aAGN,CAQA,GACEjiB,EAAM+e,cACL/J,GA4yHP,SAA0BnP,EAAaC,GACrC,GAAID,EAAEtF,WAAauF,EAAEvF,UAAYsF,EAAErF,SAAWsF,EAAEtF,OAC9C,OAAO,EAGT,GAAe,KAAXqF,EAAEpF,KAEJ,MAAkB,KAAXqF,EAAErF,KACJ,GAAIoF,EAAEpF,OAASqF,EAAErF,KAEtB,OAAO,EACF,GAAe,KAAXqF,EAAErF,KAEX,OAAO,EAKT,OAAO,CACT,CA9zHM+iB,CAAiBxjB,EAAMH,SAAUA,MAC/BuT,GAAQA,EAAKe,YAAcJ,GAAiBX,EAAKe,WAAWjD,aAG9D,YADAiR,GAAmBtiB,EAAU,CAAE4G,WAAW,CAAEwb,cAK9C7B,EAA8B,IAAI3S,gBAClC,IAMI8H,EANAnD,EAAU+I,GACZjN,EAAKjM,QACLpC,EACAugB,EAA4BxS,OAC5BwF,GAAQA,EAAKe,YAIf,GAAIf,GAAQA,EAAKsI,aAKfnG,EAAsB,CACpBsG,GAAoBpV,GAAS/B,MAAMG,GACnC,CAAE8O,KAAM1P,EAAWP,MAAOA,MAAO0P,EAAKsI,oBAEnC,GACLtI,GACAA,EAAKe,YACLJ,GAAiBX,EAAKe,WAAWjD,YACjC,CAEA,IAAIsE,QAyFRnG,eACE+C,EACAvS,EACAsU,EACA1N,EACAgd,EACArQ,QAAgD,IAAhDA,IAAAA,EAAmD,CAAA,GAKnD,IA8CI5K,EA3CJ,GANAkb,KAIAhC,GAAY,CAAEjE,WADGE,GAAwB9d,EAAUsU,IACvB,CAAE8N,WAA8B,IAAnB7O,EAAK6O,YAE1CwB,EAAY,CACd,IAAIE,QAAuBC,GACzBnd,EACA5G,EAASU,SACT6R,EAAQxE,QAEV,GAA4B,YAAxB+V,EAAehQ,KACjB,MAAO,CAAEkQ,gBAAgB,GACpB,GAA4B,UAAxBF,EAAehQ,KAAkB,CAC1C,IAAIjQ,MAAEA,EAAK4f,gBAAEA,EAAe5e,MAAEA,GAAUof,GACtCjkB,EAASU,SACTojB,GAEF,MAAO,CACLld,QAAS6c,EACT/N,oBAAqB,CACnB7Q,EAAMG,GACN,CACE8O,KAAM1P,EAAWP,MACjBA,UAIR,CAAO,IAAKigB,EAAeld,QAAS,CAClC,IAAI6c,gBAAEA,EAAe5f,MAAEA,EAAKgB,MAAEA,GAAU6e,GACtC1jB,EAASU,UAEX,MAAO,CACLkG,QAAS6c,EACT/N,oBAAqB,CACnB7Q,EAAMG,GACN,CACE8O,KAAM1P,EAAWP,MACjBA,UAIR,CACE+C,EAAUkd,EAAeld,OAE7B,CAIA,IAAIsd,EAActM,GAAehR,EAAS5G,GAE1C,GAAKkkB,EAAYrf,MAAMxC,QAAW6hB,EAAYrf,MAAMsR,KAS7C,CASL,GAFAxN,SANoBwb,GAClB,SACA5R,EACA,CAAC2R,GACDtd,IAEe,GAEb2L,EAAQxE,OAAOe,QACjB,MAAO,CAAEkV,gBAAgB,EAE7B,MApBErb,EAAS,CACPmL,KAAM1P,EAAWP,MACjBA,MAAO8P,GAAuB,IAAK,CACjCf,OAAQL,EAAQK,OAChBlS,SAAUV,EAASU,SACnB4W,QAAS4M,EAAYrf,MAAMG,MAiBjC,GAAI8W,GAAiBnT,GAAS,CAC5B,IAAI3F,EACJ,GAAIuQ,GAAwB,MAAhBA,EAAKvQ,QACfA,EAAUuQ,EAAKvQ,YACV,CASLA,EALekY,GACbvS,EAAOqS,SAAS1K,QAAQzB,IAAI,YAC5B,IAAI5L,IAAIsP,EAAQ/O,KAChBiC,KAEqBtF,EAAMH,SAASU,SAAWP,EAAMH,SAASW,MAClE,CAKA,aAJMyjB,GAAwB7R,EAAS5J,EAAQ,CAC7C2L,aACAtR,YAEK,CAAEghB,gBAAgB,EAC3B,CAEA,GAAI/H,GAAiBtT,GACnB,MAAMgL,GAAuB,IAAK,CAAEG,KAAM,iBAG5C,GAAI8B,GAAcjN,GAAS,CAGzB,IAAIoT,EAAgBC,GAAoBpV,EAASsd,EAAYrf,MAAMG,IAWnE,OAJ+B,KAA1BuO,GAAQA,EAAKvQ,WAChB8d,EAAgBC,EAAcrd,MAGzB,CACLkD,UACA8O,oBAAqB,CAACqG,EAAclX,MAAMG,GAAI2D,GAElD,CAEA,MAAO,CACL/B,UACA8O,oBAAqB,CAACwO,EAAYrf,MAAMG,GAAI2D,GAEhD,CA9N6B0b,CACvB9R,EACAvS,EACAuT,EAAKe,WACL1N,EACA4c,EAASnD,OACT,CAAErd,QAASuQ,EAAKvQ,QAASof,cAG3B,GAAIzM,EAAaqO,eACf,OAKF,GAAIrO,EAAaD,oBAAqB,CACpC,IAAK4B,EAAS3O,GAAUgN,EAAaD,oBACrC,GACEE,GAAcjN,IACdkI,EAAqBlI,EAAO9E,QACJ,MAAxB8E,EAAO9E,MAAMwM,OAWb,OATAkQ,EAA8B,UAE9B+B,GAAmBtiB,EAAU,CAC3B4G,QAAS+O,EAAa/O,QACtBM,WAAY,CAAE,EACdoP,OAAQ,CACNgB,CAACA,GAAU3O,EAAO9E,QAK1B,CAEA+C,EAAU+O,EAAa/O,SAAWA,EAClC8O,EAAsBC,EAAaD,oBACnC4N,EAAoBzF,GAAqB7d,EAAUuT,EAAKe,YACxD8N,GAAY,EAEZoB,EAASnD,QAAS,EAGlB9N,EAAU+I,GACRjN,EAAKjM,QACLmQ,EAAQ/O,IACR+O,EAAQxE,OAEZ,CAGA,IAAIiW,eACFA,EACApd,QAAS0d,EAAcpd,WACvBA,EAAUoP,OACVA,SA2KJ9G,eACE+C,EACAvS,EACA4G,EACAgd,EACAL,EACAjP,EACAiQ,EACAvhB,EACAwhB,EACApC,EACA1M,GAGA,IAAI4N,EACFC,GAAsB1F,GAAqB7d,EAAUsU,GAInDmQ,EACFnQ,GACAiQ,GACA5G,GAA4B2F,GAQ1BoB,IACDtD,GACC3O,EAAOgN,qBAAwB+E,GAOnC,GAAIZ,EAAY,CACd,GAAIc,EAA6B,CAC/B,IAAI/H,EAAagI,GAAqBjP,GACtCmM,GAAWphB,EAAA,CAEPmd,WAAY0F,QACOhe,IAAfqX,EAA2B,CAAEA,cAAe,CAAE,GAEpD,CACEyF,aAGN,CAEA,IAAI0B,QAAuBC,GACzBnd,EACA5G,EAASU,SACT6R,EAAQxE,QAGV,GAA4B,YAAxB+V,EAAehQ,KACjB,MAAO,CAAEkQ,gBAAgB,GACpB,GAA4B,UAAxBF,EAAehQ,KAAkB,CAC1C,IAAIjQ,MAAEA,EAAK4f,gBAAEA,EAAe5e,MAAEA,GAAUof,GACtCjkB,EAASU,SACTojB,GAEF,MAAO,CACLld,QAAS6c,EACTvc,WAAY,CAAE,EACdoP,OAAQ,CACN,CAACzR,EAAMG,IAAKnB,GAGlB,CAAO,IAAKigB,EAAeld,QAAS,CAClC,IAAI/C,MAAEA,EAAK4f,gBAAEA,EAAe5e,MAAEA,GAAU6e,GACtC1jB,EAASU,UAEX,MAAO,CACLkG,QAAS6c,EACTvc,WAAY,CAAE,EACdoP,OAAQ,CACN,CAACzR,EAAMG,IAAKnB,GAGlB,CACE+C,EAAUkd,EAAeld,OAE7B,CAEA,IAAI6O,EAAcwJ,GAAsBG,GACnC/F,EAAejC,GAAwBpC,GAC1C3G,EAAKjM,QACLjC,EACAyG,EACA6d,EACAzkB,EACAyS,EAAOgN,sBAA4C,IAArB+E,EAC9B/R,EAAOkN,qCACPxK,EACAC,EACAC,EACAC,GACAC,GACAC,GACAC,EACAhQ,EACAiQ,GAeF,GATAkP,IACGtN,KACG1Q,GAAWA,EAAQqC,MAAM8L,GAAMA,EAAElQ,MAAMG,KAAOsS,MAC/C+B,GAAiBA,EAAcpQ,MAAM8L,GAAMA,EAAElQ,MAAMG,KAAOsS,MAG/DiK,KAA4BD,EAGC,IAAzBjI,EAAclT,QAAgD,IAAhCiR,EAAqBjR,OAAc,CACnE,IAAI0e,EAAkBC,KAgBtB,OAfAxC,GACEtiB,EAAQS,EAAA,CAENmG,UACAM,WAAY,CAAE,EAEdoP,OACEZ,GAAuBE,GAAcF,EAAoB,IACrD,CAAE,CAACA,EAAoB,IAAKA,EAAoB,GAAG7R,OACnD,MACH6Y,GAAuBhH,GACtBmP,EAAkB,CAAEnN,SAAU,IAAIkJ,IAAIzgB,EAAMuX,WAAc,CAAE,GAElE,CAAE0K,cAEG,CAAE4B,gBAAgB,EAC3B,CAEA,GAAIU,EAA6B,CAC/B,IAAIK,EAAgC,CAAA,EACpC,IAAKnB,EAAY,CAEfmB,EAAQnH,WAAa0F,EACrB,IAAI3G,EAAagI,GAAqBjP,QACnBpQ,IAAfqX,IACFoI,EAAQpI,WAAaA,EAEzB,CACIvF,EAAqBjR,OAAS,IAChC4e,EAAQrN,SAqId,SACEN,GAUA,OARAA,EAAqBtP,SAASkd,IAC5B,IAAIvN,EAAUtX,EAAMuX,SAAS7I,IAAImW,EAAG5kB,KAChC6kB,EAAsBlH,QACxBzY,EACAmS,EAAUA,EAAQrQ,UAAO9B,GAE3BnF,EAAMuX,SAASlH,IAAIwU,EAAG5kB,IAAK6kB,EAAoB,IAE1C,IAAIrE,IAAIzgB,EAAMuX,SACvB,CAjJyBwN,CAA+B9N,IAEpDyK,GAAYkD,EAAS,CAAE3C,aACzB,CAEAhL,EAAqBtP,SAASkd,IACxB3D,EAAiB9J,IAAIyN,EAAG5kB,MAC1B+kB,GAAaH,EAAG5kB,KAEd4kB,EAAGrX,YAIL0T,EAAiB7Q,IAAIwU,EAAG5kB,IAAK4kB,EAAGrX,WAClC,IAIF,IAAIyX,EAAiCA,IACnChO,EAAqBtP,SAASuP,GAAM8N,GAAa9N,EAAEjX,OACjDmgB,GACFA,EAA4BxS,OAAO1K,iBACjC,QACA+hB,GAIJ,IAAIC,cAAEA,EAAalJ,eAAEA,SACbmJ,GACJnlB,EAAMyG,QACNA,EACAyS,EACAjC,EACA7E,GAGJ,GAAIA,EAAQxE,OAAOe,QACjB,MAAO,CAAEkV,gBAAgB,GAMvBzD,GACFA,EAA4BxS,OAAOzK,oBACjC,QACA8hB,GAGJhO,EAAqBtP,SAASkd,GAAO3D,EAAiBtS,OAAOiW,EAAG5kB,OAGhE,IAAIgQ,EAAW6M,GAAa,IAAIoI,KAAkBlJ,IAClD,GAAI/L,EAAU,CACZ,GAAIA,EAAS/P,KAAOgZ,EAAclT,OAAQ,CAIxC,IAAIof,EACFnO,EAAqBhH,EAAS/P,IAAMgZ,EAAclT,QAAQ/F,IAC5DoV,GAAiBlH,IAAIiX,EACvB,CAIA,aAHMnB,GAAwB7R,EAASnC,EAASzH,OAAQ,CACtD3F,YAEK,CAAEghB,gBAAgB,EAC3B,CAGA,IAAI9c,WAAEA,EAAUoP,OAAEA,GAAW4F,GAC3B/b,EACAyG,EACAyS,EACAgM,EACA3P,EACA0B,EACA+E,EACAV,IAIFA,GAAgB3T,SAAQ,CAAC8S,EAActD,KACrCsD,EAAaxL,WAAWN,KAIlBA,GAAW8L,EAAaxM,OAC1BqN,GAAgB1M,OAAOuI,EACzB,GACA,IAIA7E,EAAOgN,qBAAuB+E,GAAoBrkB,EAAMmW,QAC1D1M,OAAOoE,QAAQ7N,EAAMmW,QAClBpN,QAAOgF,IAAA,IAAElJ,GAAGkJ,EAAA,OAAMmL,EAAcpQ,MAAM8L,GAAMA,EAAElQ,MAAMG,KAAOA,GAAG,IAC9D8C,SAAQ8H,IAAsB,IAApB0H,EAASzT,GAAM+L,EACxB0G,EAAS1M,OAAO5F,OAAOsS,GAAU,CAAA,EAAI,CAAEgB,CAACA,GAAUzT,GAAQ,IAIhE,IAAIghB,EAAkBC,KAClBU,EAAqBC,GAAqBlE,IAC1CmE,EACFb,GAAmBW,GAAsBpO,EAAqBjR,OAAS,EAEzE,OAAA1F,EAAA,CACEmG,UACAM,aACAoP,UACIoP,EAAuB,CAAEhO,SAAU,IAAIkJ,IAAIzgB,EAAMuX,WAAc,CAAE,EAEzE,CAlbYiO,CACRpT,EACAvS,EACA4G,EACA4c,EAASnD,OACTiD,EACA/P,GAAQA,EAAKe,WACbf,GAAQA,EAAKgR,kBACbhR,GAAQA,EAAKvQ,QACbuQ,IAAkC,IAA1BA,EAAKiR,iBACbpC,EACA1M,GAGEsO,IAOJzD,EAA8B,KAE9B+B,GAAmBtiB,EAAQS,EAAA,CACzBmG,QAAS0d,GAAkB1d,GACxB8V,GAAuBhH,GAAoB,CAC9CxO,aACAoP,YAEJ,CAuZA,SAASqO,GACPjP,GAEA,OAAIA,IAAwBE,GAAcF,EAAoB,IAIrD,CACL,CAACA,EAAoB,IAAKA,EAAoB,GAAGtO,MAE1CjH,EAAMwc,WAC8B,IAAzC/S,OAAOgZ,KAAKziB,EAAMwc,YAAYxW,OACzB,KAEAhG,EAAMwc,gBAJV,CAOT,CAiiBAnN,eAAe4U,GACb7R,EACAnC,EAAwBwV,GAUxB,IATAtR,WACEA,EAAUiQ,kBACVA,EAAiBvhB,QACjBA,QAKD,IAAA4iB,EAAG,CAAA,EAAEA,EAEFxV,EAAS4K,SAAS1K,QAAQiH,IAAI,wBAChCpC,GAAyB,GAG3B,IAAInV,EAAWoQ,EAAS4K,SAAS1K,QAAQzB,IAAI,YAC7CvP,EAAUU,EAAU,uDACpBA,EAAWkb,GACTlb,EACA,IAAIiD,IAAIsP,EAAQ/O,KAChBiC,GAEF,IAAIogB,EAAmBvlB,EAAeH,EAAMH,SAAUA,EAAU,CAC9D2iB,aAAa,IAGf,GAAI9D,EAAW,CACb,IAAIiH,GAAmB,EAEvB,GAAI1V,EAAS4K,SAAS1K,QAAQiH,IAAI,2BAEhCuO,GAAmB,OACd,GAAI/T,EAAmB1I,KAAKrJ,GAAW,CAC5C,MAAMwD,EAAM6K,EAAKjM,QAAQQ,UAAU5C,GACnC8lB,EAEEtiB,EAAIV,SAAW8b,EAAa5e,SAAS8C,QAEI,MAAzC8C,EAAcpC,EAAI9C,SAAU+E,EAChC,CAEA,GAAIqgB,EAMF,YALI9iB,EACF4b,EAAa5e,SAASgD,QAAQhD,GAE9B4e,EAAa5e,SAASgE,OAAOhE,GAInC,CAIAugB,EAA8B,KAE9B,IAAIwF,GACU,IAAZ/iB,EAAmB+d,EAAc9c,QAAU8c,EAAcrd,MAIvD2N,WAAEA,EAAUC,WAAEA,EAAUC,YAAEA,GAAgBpR,EAAMyd,YAEjDtJ,IACAiQ,GACDlT,GACAC,GACAC,IAEA+C,EAAaqJ,GAA4Bxd,EAAMyd,aAMjD,IAAI6G,EAAmBnQ,GAAciQ,EACrC,GACEpT,EAAkCoG,IAAInH,EAAS4K,SAAS3K,SACxDoU,GACAvQ,GAAiBuQ,EAAiBpT,kBAE5B4R,GAAgB8C,EAAuBF,EAAkB,CAC7DvR,WAAU7T,EAAA,CAAA,EACLgkB,EAAgB,CACnBnT,WAAYtR,IAGd0gB,mBAAoBM,QAEjB,CAGL,IAAIuC,EAAqB1F,GACvBgI,EACAvR,SAEI2O,GAAgB8C,EAAuBF,EAAkB,CAC7DtC,qBAEAgB,oBAEA7D,mBAAoBM,GAExB,CACF,CAIAxR,eAAe2U,GACbrQ,EACAvB,EACA8G,EACAzS,GAEA,IACE,IAAI6S,QAAgBN,GAClBC,EACAtF,EACAvB,EACA8G,EACAzS,EACAjC,EACAF,GAGF,aAAagJ,QAAQyL,IACnBO,EAAQ7U,KAAI,CAAC+D,EAAQrC,KACnB,GAAI4W,GAAwBvU,GAAS,CACnC,IAAIqS,EAAWrS,EAAOA,OACtB,MAAO,CACLmL,KAAM1P,EAAWgM,SACjB4K,SAAUD,GACRC,EACAzI,EACA8G,EAAc/S,GAAGzB,MAAMG,GACvB4B,EACAnB,EACAgN,EAAO3G,sBAGb,CAEA,OAAOwO,GAAiC3R,EAAO,IAUrD,CAPE,MAAO7I,GAGP,OAAOuZ,EAAczU,KAAI,KAAO,CAC9BkP,KAAM1P,EAAWP,MACjBA,MAAO/D,KAEX,CACF,CAEA0P,eAAe8V,GACbjI,EACAzW,EACAyS,EACA2M,EACAzT,GAEA,IAAK8S,KAAkBlJ,SAAwB1O,QAAQyL,IAAI,CACzDG,EAAclT,OACVge,GAAiB,SAAU5R,EAAS8G,EAAezS,GACnD,MACDof,EAAephB,KAAKyS,IACrB,GAAIA,EAAEzQ,SAAWyQ,EAAEpQ,OAASoQ,EAAE1J,WAAY,CAMxC,OAAOwW,GACL,SANmB7I,GACnBjN,EAAKjM,QACLiV,EAAEhW,KACFgW,EAAE1J,WAAWI,QAKb,CAACsJ,EAAEpQ,OACHoQ,EAAEzQ,SACF6H,MAAMf,GAAMA,EAAE,IAClB,CACE,OAAOD,QAAQgC,QAAoB,CACjCqE,KAAM1P,EAAWP,MACjBA,MAAO8P,GAAuB,IAAK,CACjCjT,SAAU2W,EAAEhW,QAGlB,MAsBJ,aAlBMoM,QAAQyL,IAAI,CAChBkE,GACEC,EACAhE,EACAgM,EACAA,EAAczgB,KAAI,IAAM2N,EAAQxE,UAChC,EACA5N,EAAM+G,YAERkW,GACEC,EACA2I,EAAephB,KAAKyS,GAAMA,EAAEpQ,QAC5BkV,EACA6J,EAAephB,KAAKyS,GAAOA,EAAE1J,WAAa0J,EAAE1J,WAAWI,OAAS,QAChE,KAIG,CACLsX,gBACAlJ,iBAEJ,CAEA,SAAS0H,KAEP1O,GAAyB,EAIzBC,EAAwB3R,QAAQmhB,MAGhCrP,GAAiBzN,SAAQ,CAACqC,EAAG/J,KACvBihB,EAAiB9J,IAAInX,KACvBiV,EAAsB5R,KAAKrD,GAC3B+kB,GAAa/kB,GACf,GAEJ,CAEA,SAAS6lB,GACP7lB,EACAqX,EACAlE,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhCpT,EAAMuX,SAASlH,IAAIpQ,EAAKqX,GACxBoK,GACE,CAAEnK,SAAU,IAAIkJ,IAAIzgB,EAAMuX,WAC1B,CAAE0K,WAAwC,KAA5B7O,GAAQA,EAAK6O,YAE/B,CAEA,SAAS8D,GACP9lB,EACAkX,EACAzT,EACA0P,QAA6B,IAA7BA,IAAAA,EAAgC,CAAA,GAEhC,IAAIwI,EAAgBC,GAAoB7b,EAAMyG,QAAS0Q,GACvD+K,GAAcjiB,GACdyhB,GACE,CACEvL,OAAQ,CACN,CAACyF,EAAclX,MAAMG,IAAKnB,GAE5B6T,SAAU,IAAIkJ,IAAIzgB,EAAMuX,WAE1B,CAAE0K,WAAwC,KAA5B7O,GAAQA,EAAK6O,YAE/B,CAEA,SAAS+D,GAAwB/lB,GAS/B,OARIqS,EAAO8M,oBACTkC,GAAejR,IAAIpQ,GAAMqhB,GAAe5S,IAAIzO,IAAQ,GAAK,GAGrDkV,GAAgBiC,IAAInX,IACtBkV,GAAgBvG,OAAO3O,IAGpBD,EAAMuX,SAAS7I,IAAIzO,IAAQuR,CACpC,CAEA,SAAS0Q,GAAcjiB,GACrB,IAAIqX,EAAUtX,EAAMuX,SAAS7I,IAAIzO,IAK/BihB,EAAiB9J,IAAInX,IACnBqX,GAA6B,YAAlBA,EAAQtX,OAAuBqhB,GAAejK,IAAInX,IAE/D+kB,GAAa/kB,GAEfmV,GAAiBxG,OAAO3O,GACxBohB,GAAezS,OAAO3O,GACtBoV,GAAiBzG,OAAO3O,GACxBkV,GAAgBvG,OAAO3O,GACvBD,EAAMuX,SAAS3I,OAAO3O,EACxB,CAiBA,SAAS+kB,GAAa/kB,GACpB,IAAIuN,EAAa0T,EAAiBxS,IAAIzO,GACtCd,EAAUqO,EAA0CvN,8BAAAA,GACpDuN,EAAW2B,QACX+R,EAAiBtS,OAAO3O,EAC1B,CAEA,SAASgmB,GAAiBxD,GACxB,IAAK,IAAIxiB,KAAOwiB,EAAM,CACpB,IACIxG,EAAcC,GADJ8J,GAAW/lB,GACgBgH,MACzCjH,EAAMuX,SAASlH,IAAIpQ,EAAKgc,EAC1B,CACF,CAEA,SAAS0I,KACP,IAAIuB,EAAW,GACXxB,GAAkB,EACtB,IAAK,IAAIzkB,KAAOoV,GAAkB,CAChC,IAAIiC,EAAUtX,EAAMuX,SAAS7I,IAAIzO,GACjCd,EAAUmY,EAA8BrX,qBAAAA,GAClB,YAAlBqX,EAAQtX,QACVqV,GAAiBzG,OAAO3O,GACxBimB,EAAS5iB,KAAKrD,GACdykB,GAAkB,EAEtB,CAEA,OADAuB,GAAiBC,GACVxB,CACT,CAEA,SAASY,GAAqBa,GAC5B,IAAIC,EAAa,GACjB,IAAK,IAAKnmB,EAAK4E,KAAOwc,GACpB,GAAIxc,EAAKshB,EAAU,CACjB,IAAI7O,EAAUtX,EAAMuX,SAAS7I,IAAIzO,GACjCd,EAAUmY,EAA8BrX,qBAAAA,GAClB,YAAlBqX,EAAQtX,QACVglB,GAAa/kB,GACbohB,GAAezS,OAAO3O,GACtBmmB,EAAW9iB,KAAKrD,GAEpB,CAGF,OADAgmB,GAAiBG,GACVA,EAAWpgB,OAAS,CAC7B,CAYA,SAASqgB,GAAcpmB,GACrBD,EAAM0gB,SAAS9R,OAAO3O,GACtBshB,GAAiB3S,OAAO3O,EAC1B,CAGA,SAASqmB,GAAcrmB,EAAasmB,GAClC,IAAIC,EAAUxmB,EAAM0gB,SAAShS,IAAIzO,IAAQwR,EAIzCtS,EACqB,cAAlBqnB,EAAQxmB,OAA8C,YAArBumB,EAAWvmB,OACxB,YAAlBwmB,EAAQxmB,OAA4C,YAArBumB,EAAWvmB,OACxB,YAAlBwmB,EAAQxmB,OAA4C,eAArBumB,EAAWvmB,OACxB,YAAlBwmB,EAAQxmB,OAA4C,cAArBumB,EAAWvmB,OACxB,eAAlBwmB,EAAQxmB,OAA+C,cAArBumB,EAAWvmB,MAAsB,qCACjCwmB,EAAQxmB,MAAK,OAAOumB,EAAWvmB,OAGtE,IAAI0gB,EAAW,IAAID,IAAIzgB,EAAM0gB,UAC7BA,EAASrQ,IAAIpQ,EAAKsmB,GAClB7E,GAAY,CAAEhB,YAChB,CAEA,SAAS+F,GAAqBC,GAQP,IARQ/D,gBAC7BA,EAAepE,aACfA,EAAY8B,cACZA,GAKDqG,EACC,GAA8B,IAA1BnF,GAAiBhS,KACnB,OAKEgS,GAAiBhS,KAAO,GAC1BhQ,GAAQ,EAAO,gDAGjB,IAAIsO,EAAUV,MAAMjB,KAAKqV,GAAiB1T,YACrC8Y,EAAYC,GAAmB/Y,EAAQA,EAAQ7H,OAAS,GACzDwgB,EAAUxmB,EAAM0gB,SAAShS,IAAIiY,GAEjC,OAAIH,GAA6B,eAAlBA,EAAQxmB,WAAvB,EAQI4mB,EAAgB,CAAEjE,kBAAiBpE,eAAc8B,kBAC5CsG,OADT,CAGF,CAEA,SAASpD,GAAsBhjB,GAC7B,IAAImD,EAAQ8P,GAAuB,IAAK,CAAEjT,aACtC+U,EAAcwJ,GAAsBG,GACpCxY,QAAEA,EAAO/B,MAAEA,GAAUiY,GAAuBrH,GAKhD,OAFAmP,KAEO,CAAEnB,gBAAiB7c,EAAS/B,QAAOhB,QAC5C,CAEA,SAASogB,GACPvjB,EACAojB,GAEA,IAAIld,EAAUkd,EAAekD,eACzBniB,EAAQ+B,EAAQA,EAAQT,OAAS,GAAGtB,MAUxC,MAAO,CAAE4e,gBAAiB7c,EAAS/B,QAAOhB,MAT9B8P,GAAuB,IAAK,CACtCG,KAAM,kBACNwD,QAASzS,EAAMG,GACftE,WACAlB,QAC0B,MAAxBskB,EAAejgB,OAAiB,YAAaigB,EAAejgB,MACxDigB,EAAejgB,MACfkB,OAAO+e,EAAejgB,SAGhC,CAEA,SAAS+gB,GACPqC,GAEA,IAAIC,EAA8B,GAWlC,OAVAzL,GAAgB3T,SAAQ,CAACqf,EAAK7P,KACvB2P,IAAaA,EAAU3P,KAI1B6P,EAAI9X,SACJ6X,EAAkBzjB,KAAK6T,GACvBmE,GAAgB1M,OAAOuI,GACzB,IAEK4P,CACT,CA+BA,SAAS/D,GAAanjB,EAAoB4G,GACxC,GAAIkZ,EAAyB,CAK3B,OAJUA,EACR9f,EACA4G,EAAQhC,KAAKmQ,GAAM/N,EAA2B+N,EAAG5U,EAAM+G,gBAE3ClH,EAASI,GACzB,CACA,OAAOJ,EAASI,GAClB,CAYA,SAAS4iB,GACPhjB,EACA4G,GAEA,GAAIiZ,EAAsB,CACxB,IAAIzf,EAAM+iB,GAAanjB,EAAU4G,GAC7BwgB,EAAIvH,EAAqBzf,GAC7B,GAAiB,iBAANgnB,EACT,OAAOA,CAEX,CACA,OAAO,IACT,CAEA,SAAShH,GACPxZ,EACA6O,EACA/U,GAEA,GAAIyX,EAAuB,CACzB,IAAKvR,EAAS,CAQZ,MAAO,CAAEyZ,QAAQ,EAAMzZ,QAPNlB,EACf+P,EACA/U,EACA+E,GACA,IAG4C,GAChD,CAAO,CACL,IAAI4hB,EAAYzgB,EAAQA,EAAQT,OAAS,GAAGtB,MAC5C,GACEwiB,EAAUhmB,OACU,MAAnBgmB,EAAUhmB,MAAgBgmB,EAAUhmB,KAAKmH,SAAS,OACnD,CAUA,MAAO,CAAE6X,QAAQ,EAAMzZ,QANFlB,EACnB+P,EACA/U,EACA+E,GACA,GAGJ,CACF,CACF,CAEA,MAAO,CAAE4a,QAAQ,EAAOzZ,QAAS,KACnC,CAiBA4I,eAAeuU,GACbnd,EACAlG,EACAqN,GAEA,IAAIiZ,EAAkDpgB,EAClD/B,EACFmiB,EAAe7gB,OAAS,EACpB6gB,EAAeA,EAAe7gB,OAAS,GAAGtB,MAC1C,KACN,OAAa,CACX,IAAIyiB,EAAiC,MAAtBrI,EACXxJ,EAAcwJ,GAAsBG,EACxC,UACQlH,GACJC,EACAzX,EACAsmB,EACAvR,EACA9Q,EACAF,EACAkd,GACA5T,EAcJ,CAZE,MAAOjO,GACP,MAAO,CAAEgU,KAAM,QAASjQ,MAAO/D,EAAGknB,iBACpC,CAAU,QAOJM,IACFlI,EAAa,IAAIA,GAErB,CAEA,GAAIrR,EAAOe,QACT,MAAO,CAAEgF,KAAM,WAGjB,IAAIyT,EAAahiB,EAAYkQ,EAAa/U,EAAU+E,GAChD+hB,GAAe,EACnB,GAAID,EAAY,CACd,IAAIF,EAAYE,EAAWA,EAAWphB,OAAS,GAAGtB,MAElD,GAAIwiB,EAAUpnB,MAEZ,MAAO,CAAE6T,KAAM,UAAWlN,QAAS2gB,GAGrC,GAAIF,EAAUhmB,MAAQgmB,EAAUhmB,KAAK8E,OAAS,EAAG,CAC/C,GAAuB,MAAnBkhB,EAAUhmB,KAOZ,MAAO,CAAEyS,KAAM,UAAWlN,QAAS2gB,GAHnCC,GAAe,CAKnB,CACF,CAEA,IAAIC,EAAoB/hB,EACtB+P,EACA/U,EACA+E,GACA,GAMF,IACGgiB,GACDT,EAAepiB,KAAKmQ,GAAMA,EAAElQ,MAAMG,KAAIC,KAAK,OACzCwiB,EAAkB7iB,KAAKmQ,GAAMA,EAAElQ,MAAMG,KAAIC,KAAK,KAEhD,MAAO,CAAE6O,KAAM,UAAWlN,QAAS4gB,EAAeD,EAAa,MAKjE,GAFAP,EAAiBS,EACjB5iB,EAAQmiB,EAAeA,EAAe7gB,OAAS,GAAGtB,MAC/B,MAAfA,EAAMxD,KAER,MAAO,CAAEyS,KAAM,UAAWlN,QAASogB,EAEvC,CACF,CA4EA,OAvCA7H,EAAS,CACH1Z,eACF,OAAOA,CACR,EACGgN,aACF,OAAOA,CACR,EACGtS,YACF,OAAOA,CACR,EACGqE,aACF,OAAO4a,CACR,EACGrd,aACF,OAAO6c,CACR,EACD8I,WAlyEF,WA4DE,GAzDA9H,EAAkBvR,EAAKjM,QAAQe,QAC7BhC,IAAgD,IAA7CkB,OAAQme,EAAaxgB,SAAEA,EAAQ2C,MAAEA,GAAOxB,EAGzC,GAAIygB,GAEF,YADAA,IAA0B,GAI5BliB,EAC4B,IAA1BgiB,GAAiBhS,MAAuB,MAAT/M,EAC/B,8YAQF,IAAImkB,EAAaF,GAAsB,CACrC9D,gBAAiB3iB,EAAMH,SACvB0e,aAAc1e,EACdwgB,kBAGF,OAAIsG,GAAuB,MAATnkB,GAEhBif,IAA0B,EAC1BvT,EAAKjM,QAAQ8B,IAAY,EAATvB,QAGhB8jB,GAAcK,EAAY,CACxB3mB,MAAO,UACPH,WACA6R,UACE4U,GAAcK,EAAa,CACzB3mB,MAAO,aACP0R,aAASvM,EACTwM,WAAOxM,EACPtF,aAGFqO,EAAKjM,QAAQ8B,GAAGvB,EACjB,EACDmP,QACE,IAAI+O,EAAW,IAAID,IAAIzgB,EAAM0gB,UAC7BA,EAASrQ,IAAIsW,EAAalV,GAC1BiQ,GAAY,CAAEhB,YAChB,KAKGoC,GAAgBzC,EAAexgB,EAAS,IAI/C6e,EAAW,EA4kJnB,SACE8I,EACAC,GAEA,IACE,IAAIC,EAAmBF,EAAQG,eAAeC,QAC5C5V,GAEF,GAAI0V,EAAkB,CACpB,IAAIpW,EAAO/F,KAAK6I,MAAMsT,GACtB,IAAK,IAAKtY,EAAG7E,KAAMd,OAAOoE,QAAQyD,GAAQ,CAAA,GACpC/G,GAAK4C,MAAMC,QAAQ7C,IACrBkd,EAAYpX,IAAIjB,EAAG,IAAIjL,IAAIoG,GAAK,IAGtC,CAEA,CADA,MAAO5K,GACP,CAEJ,CA5lJMkoB,CAA0BpJ,EAAcsC,GACxC,IAAI+G,EAA0BA,IA6lJpC,SACEN,EACAC,GAEA,GAAIA,EAAYlY,KAAO,EAAG,CACxB,IAAI+B,EAAiC,CAAA,EACrC,IAAK,IAAKlC,EAAG7E,KAAMkd,EACjBnW,EAAKlC,GAAK,IAAI7E,GAEhB,IACEid,EAAQG,eAAeI,QACrB/V,EACAzG,KAAKC,UAAU8F,GAOnB,CALE,MAAO5N,GACPnE,GACE,EAC8DmE,8DAAAA,OAElE,CACF,CACF,CAjnJQskB,CAA0BvJ,EAAcsC,GAC1CtC,EAAavb,iBAAiB,WAAY4kB,GAC1C9G,EAA8BA,IAC5BvC,EAAatb,oBAAoB,WAAY2kB,EACjD,CAaA,OANK9nB,EAAM+e,aACT+D,GAAgBlC,EAAcze,IAAKnC,EAAMH,SAAU,CACjDwkB,kBAAkB,IAIfrF,CACT,EAgtEE/P,UA/rEF,SAAmBhM,GAEjB,OADAgK,EAAYkB,IAAIlL,GACT,IAAMgK,EAAY2B,OAAO3L,EAClC,EA6rEEglB,wBApQF,SACEC,EACAC,EACAC,GASA,GAPA1I,EAAuBwI,EACvBtI,EAAoBuI,EACpBxI,EAA0ByI,GAAU,MAK/BvI,GAAyB7f,EAAMyd,aAAexM,EAAiB,CAClE4O,GAAwB,EACxB,IAAIoH,EAAIpE,GAAuB7iB,EAAMH,SAAUG,EAAMyG,SAC5C,MAALwgB,GACFvF,GAAY,CAAEpB,sBAAuB2G,GAEzC,CAEA,MAAO,KACLvH,EAAuB,KACvBE,EAAoB,KACpBD,EAA0B,IAAI,CAElC,EA4OE0I,SAp/DFhZ,eAAegZ,EACbhoB,EACA+S,GAEA,GAAkB,iBAAP/S,EAET,YADA6N,EAAKjM,QAAQ8B,GAAG1D,GAIlB,IAAIioB,EAAiB5V,GACnB1S,EAAMH,SACNG,EAAMyG,QACNnB,EACAgN,EAAOiN,mBACPlf,EACAiS,EAAO3G,qBACPyH,MAAAA,OAAAA,EAAAA,EAAMR,YACF,MAAJQ,OAAI,EAAJA,EAAMP,WAEJ3R,KAAEA,EAAIiT,WAAEA,EAAUzQ,MAAEA,GAAUuP,GAChCX,EAAO+M,wBACP,EACAiJ,EACAlV,GAGEuP,EAAkB3iB,EAAMH,SACxB0e,EAAepe,EAAeH,EAAMH,SAAUqB,EAAMkS,GAAQA,EAAKpT,OAOrEue,EAAYje,EACPie,CAAAA,EAAAA,EACArQ,EAAKjM,QAAQmB,eAAemb,IAGjC,IAAIgK,EAAcnV,GAAwB,MAAhBA,EAAKvQ,QAAkBuQ,EAAKvQ,aAAUsC,EAE5Dkb,EAAgBO,EAAcrd,MAEd,IAAhBglB,EACFlI,EAAgBO,EAAc9c,SACL,IAAhBykB,GAGK,MAAdpU,GACAJ,GAAiBI,EAAWjD,aAC5BiD,EAAWhD,aAAenR,EAAMH,SAASU,SAAWP,EAAMH,SAASW,SAMnE6f,EAAgBO,EAAc9c,SAGhC,IAAIyc,EACFnN,GAAQ,uBAAwBA,GACA,IAA5BA,EAAKmN,wBACLpb,EAEF8c,GAAkD,KAArC7O,GAAQA,EAAK4O,oBAE1B2E,EAAaF,GAAsB,CACrC9D,kBACApE,eACA8B,kBAGF,IAAIsG,EAwBJ,aAAa7D,GAAgBzC,EAAe9B,EAAc,CACxDpK,aAGAuH,aAAchY,EACd6c,qBACA1d,QAASuQ,GAAQA,EAAKvQ,QACtBqgB,qBAAsB9P,GAAQA,EAAKoV,wBACnCvG,cA9BAqE,GAAcK,EAAY,CACxB3mB,MAAO,UACPH,SAAU0e,EACV7M,UACE4U,GAAcK,EAAa,CACzB3mB,MAAO,aACP0R,aAASvM,EACTwM,WAAOxM,EACPtF,SAAU0e,IAGZ8J,EAAShoB,EAAI+S,EACd,EACDzB,QACE,IAAI+O,EAAW,IAAID,IAAIzgB,EAAM0gB,UAC7BA,EAASrQ,IAAIsW,EAAalV,GAC1BiQ,GAAY,CAAEhB,YAChB,GAeN,EA24DE+H,MAjvCF,SACExoB,EACAkX,EACAvU,EACAwQ,GAEA,GAAIwL,EACF,MAAM,IAAItf,MACR,oMAMA4hB,EAAiB9J,IAAInX,IAAM+kB,GAAa/kB,GAC5C,IAAIgiB,GAAkD,KAArC7O,GAAQA,EAAK4O,oBAE1B1M,EAAcwJ,GAAsBG,EACpCqJ,EAAiB5V,GACnB1S,EAAMH,SACNG,EAAMyG,QACNnB,EACAgN,EAAOiN,mBACP3c,EACA0P,EAAO3G,qBACPwL,EACI,MAAJ/D,OAAI,EAAJA,EAAMP,UAEJpM,EAAUrB,EAAYkQ,EAAagT,EAAgBhjB,GAEnD+d,EAAWpD,GAAcxZ,EAAS6O,EAAagT,GAKnD,GAJIjF,EAASnD,QAAUmD,EAAS5c,UAC9BA,EAAU4c,EAAS5c,UAGhBA,EAOH,YANAsf,GACE9lB,EACAkX,EACA3D,GAAuB,IAAK,CAAEjT,SAAU+nB,IACxC,CAAErG,cAKN,IAAI/gB,KAAEA,EAAIiT,WAAEA,EAAUzQ,MAAEA,GAAUuP,GAChCX,EAAO+M,wBACP,EACAiJ,EACAlV,GAGF,GAAI1P,EAEF,YADAqiB,GAAgB9lB,EAAKkX,EAASzT,EAAO,CAAEue,cAIzC,IAAInb,EAAQ2Q,GAAehR,EAASvF,GAEpC2f,GAAkE,KAArCzN,GAAQA,EAAKmN,oBAEtCpM,GAAcJ,GAAiBI,EAAWjD,YA+BhD7B,eACEpP,EACAkX,EACAjW,EACA4F,EACA4hB,EACAjF,EACAxB,EACA9N,GAKA,SAASwU,EAAwB/T,GAC/B,IAAKA,EAAElQ,MAAMxC,SAAW0S,EAAElQ,MAAMsR,KAAM,CACpC,IAAItS,EAAQ8P,GAAuB,IAAK,CACtCf,OAAQ0B,EAAWjD,WACnB3Q,SAAUW,EACViW,QAASA,IAGX,OADA4O,GAAgB9lB,EAAKkX,EAASzT,EAAO,CAAEue,eAChC,CACT,CACA,OAAO,CACT,CAEA,GAhBAyB,KACAtO,GAAiBxG,OAAO3O,IAenBwjB,GAAckF,EAAwB7hB,GACzC,OAIF,IAAI8hB,EAAkB5oB,EAAMuX,SAAS7I,IAAIzO,GACzC6lB,GAAmB7lB,EAw7GvB,SACEkU,EACAyU,GAYA,MAV2C,CACzC5oB,MAAO,aACPkR,WAAYiD,EAAWjD,WACvBC,WAAYgD,EAAWhD,WACvBC,YAAa+C,EAAW/C,YACxBC,SAAU8C,EAAW9C,SACrBC,KAAM6C,EAAW7C,KACjBC,KAAM4C,EAAW5C,KACjBtK,KAAM2hB,EAAkBA,EAAgB3hB,UAAO9B,EAGnD,CAv8G4B0jB,CAAqB1U,EAAYyU,GAAkB,CACzE3G,cAGF,IAAI6G,EAAkB,IAAIrb,gBACtBsb,EAAe5N,GACjBjN,EAAKjM,QACLf,EACA4nB,EAAgBlb,OAChBuG,GAGF,GAAIsP,EAAY,CACd,IAAIE,QAAuBC,GACzB8E,EACAxnB,EACA6nB,EAAanb,QAGf,GAA4B,YAAxB+V,EAAehQ,KACjB,OACK,GAA4B,UAAxBgQ,EAAehQ,KAAkB,CAC1C,IAAIjQ,MAAEA,GAAUogB,GAAyB5iB,EAAMyiB,GAE/C,YADAoC,GAAgB9lB,EAAKkX,EAASzT,EAAO,CAAEue,aAEzC,CAAO,IAAK0B,EAAeld,QAOzB,YANAsf,GACE9lB,EACAkX,EACA3D,GAAuB,IAAK,CAAEjT,SAAUW,IACxC,CAAE+gB,cAOJ,GAAI0G,EAFJ7hB,EAAQ2Q,GADRiR,EAAiB/E,EAAeld,QACOvF,IAGrC,MAGN,CAGAggB,EAAiB7Q,IAAIpQ,EAAK6oB,GAE1B,IAAIE,EAAoB7H,EAOpB3L,SANsBwO,GACxB,SACA+E,EACA,CAACjiB,GACD4hB,IAE+B,GAEjC,GAAIK,EAAanb,OAAOe,QAMtB,YAHIuS,EAAiBxS,IAAIzO,KAAS6oB,GAChC5H,EAAiBtS,OAAO3O,IAQ5B,GAAIqS,EAAO8M,mBAAqBjK,GAAgBiC,IAAInX,IAClD,GAAI0b,GAAiBnG,IAAiBC,GAAcD,GAElD,YADAsQ,GAAmB7lB,EAAKic,QAAe/W,QAIpC,CACL,GAAIwW,GAAiBnG,GAEnB,OADA0L,EAAiBtS,OAAO3O,GACpBmhB,GAA0B4H,OAK5BlD,GAAmB7lB,EAAKic,QAAe/W,KAGvCkQ,GAAiBlH,IAAIlO,GACrB6lB,GAAmB7lB,EAAK2d,GAAkBzJ,IACnC8P,GAAwB8E,EAAcvT,EAAc,CACzD4O,kBAAmBjQ,KAMzB,GAAIsB,GAAcD,GAEhB,YADAuQ,GAAgB9lB,EAAKkX,EAAS3B,EAAa9R,MAG/C,CAEA,GAAIoY,GAAiBtG,GACnB,MAAMhC,GAAuB,IAAK,CAAEG,KAAM,iBAK5C,IAAI4K,EAAeve,EAAMyd,WAAW5d,UAAYG,EAAMH,SAClDopB,EAAsB9N,GACxBjN,EAAKjM,QACLsc,EACAuK,EAAgBlb,QAEd0H,EAAcwJ,GAAsBG,EACpCxY,EACyB,SAA3BzG,EAAMyd,WAAWzd,MACboF,EAAYkQ,EAAatV,EAAMyd,WAAW5d,SAAUyF,GACpDtF,EAAMyG,QAEZtH,EAAUsH,EAAS,gDAEnB,IAAIyiB,IAAW/H,EACfE,GAAehR,IAAIpQ,EAAKipB,GAExB,IAAIC,EAAcvL,GAAkBzJ,EAAYqB,EAAavO,MAC7DjH,EAAMuX,SAASlH,IAAIpQ,EAAKkpB,GAExB,IAAKjQ,EAAejC,GAAwBpC,GAC1C3G,EAAKjM,QACLjC,EACAyG,EACA0N,EACAoK,GACA,EACAjM,EAAOkN,qCACPxK,EACAC,EACAC,EACAC,GACAC,GACAC,GACAC,EACAhQ,EACA,CAACwB,EAAMpC,MAAMG,GAAI2Q,IAMnByB,EACGlO,QAAQ8b,GAAOA,EAAG5kB,MAAQA,IAC1B0H,SAASkd,IACR,IAAIuE,EAAWvE,EAAG5kB,IACd2oB,EAAkB5oB,EAAMuX,SAAS7I,IAAI0a,GACrCtE,EAAsBlH,QACxBzY,EACAyjB,EAAkBA,EAAgB3hB,UAAO9B,GAE3CnF,EAAMuX,SAASlH,IAAI+Y,EAAUtE,GACzB5D,EAAiB9J,IAAIgS,IACvBpE,GAAaoE,GAEXvE,EAAGrX,YACL0T,EAAiB7Q,IAAI+Y,EAAUvE,EAAGrX,WACpC,IAGJkU,GAAY,CAAEnK,SAAU,IAAIkJ,IAAIzgB,EAAMuX,YAEtC,IAAI0N,EAAiCA,IACnChO,EAAqBtP,SAASkd,GAAOG,GAAaH,EAAG5kB,OAEvD6oB,EAAgBlb,OAAO1K,iBACrB,QACA+hB,GAGF,IAAIC,cAAEA,EAAalJ,eAAEA,SACbmJ,GACJnlB,EAAMyG,QACNA,EACAyS,EACAjC,EACAgS,GAGJ,GAAIH,EAAgBlb,OAAOe,QACzB,OAGFma,EAAgBlb,OAAOzK,oBACrB,QACA8hB,GAGF5D,GAAezS,OAAO3O,GACtBihB,EAAiBtS,OAAO3O,GACxBgX,EAAqBtP,SAAS4F,GAAM2T,EAAiBtS,OAAOrB,EAAEtN,OAE9D,IAAIgQ,EAAW6M,GAAa,IAAIoI,KAAkBlJ,IAClD,GAAI/L,EAAU,CACZ,GAAIA,EAAS/P,KAAOgZ,EAAclT,OAAQ,CAIxC,IAAIof,EACFnO,EAAqBhH,EAAS/P,IAAMgZ,EAAclT,QAAQ/F,IAC5DoV,GAAiBlH,IAAIiX,EACvB,CACA,OAAOnB,GAAwBgF,EAAqBhZ,EAASzH,OAC/D,CAGA,IAAIzB,WAAEA,EAAUoP,OAAEA,GAAW4F,GAC3B/b,EACAA,EAAMyG,QACNyS,EACAgM,OACA/f,EACA8R,EACA+E,EACAV,IAKF,GAAItb,EAAMuX,SAASH,IAAInX,GAAM,CAC3B,IAAIgc,EAAcC,GAAe1G,EAAavO,MAC9CjH,EAAMuX,SAASlH,IAAIpQ,EAAKgc,EAC1B,CAEAqJ,GAAqB4D,GAMQ,YAA3BlpB,EAAMyd,WAAWzd,OACjBkpB,EAAS9H,IAETjiB,EAAUwhB,EAAe,2BACzBP,GAA+BA,EAA4BjR,QAE3DgT,GAAmBniB,EAAMyd,WAAW5d,SAAU,CAC5C4G,UACAM,aACAoP,SACAoB,SAAU,IAAIkJ,IAAIzgB,EAAMuX,cAM1BmK,GAAY,CACVvL,SACApP,WAAYoV,GACVnc,EAAM+G,WACNA,EACAN,EACA0P,GAEFoB,SAAU,IAAIkJ,IAAIzgB,EAAMuX,YAE1BvC,GAAyB,EAE7B,CArUIqU,CACEppB,EACAkX,EACAjW,EACA4F,EACAL,EACA4c,EAASnD,OACT+B,EACA9N,IAOJiB,GAAiB/E,IAAIpQ,EAAK,CAAEkX,UAASjW,SAyTvCmO,eACEpP,EACAkX,EACAjW,EACA4F,EACAL,EACAgd,EACAxB,EACA9N,GAEA,IAAIyU,EAAkB5oB,EAAMuX,SAAS7I,IAAIzO,GACzC6lB,GACE7lB,EACA2d,GACEzJ,EACAyU,EAAkBA,EAAgB3hB,UAAO9B,GAE3C,CAAE8c,cAGJ,IAAI6G,EAAkB,IAAIrb,gBACtBsb,EAAe5N,GACjBjN,EAAKjM,QACLf,EACA4nB,EAAgBlb,QAGlB,GAAI6V,EAAY,CACd,IAAIE,QAAuBC,GACzBnd,EACAvF,EACA6nB,EAAanb,QAGf,GAA4B,YAAxB+V,EAAehQ,KACjB,OACK,GAA4B,UAAxBgQ,EAAehQ,KAAkB,CAC1C,IAAIjQ,MAAEA,GAAUogB,GAAyB5iB,EAAMyiB,GAE/C,YADAoC,GAAgB9lB,EAAKkX,EAASzT,EAAO,CAAEue,aAEzC,CAAO,IAAK0B,EAAeld,QAOzB,YANAsf,GACE9lB,EACAkX,EACA3D,GAAuB,IAAK,CAAEjT,SAAUW,IACxC,CAAE+gB,cAKJnb,EAAQ2Q,GADRhR,EAAUkd,EAAeld,QACOvF,EAEpC,CAGAggB,EAAiB7Q,IAAIpQ,EAAK6oB,GAE1B,IAAIE,EAAoB7H,EAOpB3Y,SANgBwb,GAClB,SACA+E,EACA,CAACjiB,GACDL,IAEmB,GAMjBqV,GAAiBtT,KACnBA,QACS6U,GAAoB7U,EAAQugB,EAAanb,QAAQ,IACxDpF,GAKA0Y,EAAiBxS,IAAIzO,KAAS6oB,GAChC5H,EAAiBtS,OAAO3O,GAG1B,GAAI8oB,EAAanb,OAAOe,QACtB,OAKF,GAAIwG,GAAgBiC,IAAInX,GAEtB,YADA6lB,GAAmB7lB,EAAKic,QAAe/W,IAKzC,GAAIwW,GAAiBnT,GACnB,OAAI4Y,GAA0B4H,OAG5BlD,GAAmB7lB,EAAKic,QAAe/W,KAGvCkQ,GAAiBlH,IAAIlO,cACfgkB,GAAwB8E,EAAcvgB,IAMhD,GAAIiN,GAAcjN,GAEhB,YADAud,GAAgB9lB,EAAKkX,EAAS3O,EAAO9E,OAIvCvE,GAAW2c,GAAiBtT,GAAS,mCAGrCsd,GAAmB7lB,EAAKic,GAAe1T,EAAOvB,MAChD,CA7aEqiB,CACErpB,EACAkX,EACAjW,EACA4F,EACAL,EACA4c,EAASnD,OACT+B,EACA9N,GAEJ,EA0pCEoV,WAv4DF,WACE7F,KACAhC,GAAY,CAAElB,aAAc,YAIG,eAA3BxgB,EAAMyd,WAAWzd,QAOU,SAA3BA,EAAMyd,WAAWzd,MAUrB8iB,GACEnC,GAAiB3gB,EAAMqgB,cACvBrgB,EAAMyd,WAAW5d,SACjB,CAAEujB,mBAAoBpjB,EAAMyd,aAZ5BqF,GAAgB9iB,EAAMqgB,cAAergB,EAAMH,SAAU,CACnDkjB,gCAAgC,IAatC,EA82DEthB,WAAapB,GAAW6N,EAAKjM,QAAQR,WAAWpB,GAChD+C,eAAiB/C,GAAW6N,EAAKjM,QAAQmB,eAAe/C,GACxD2lB,cACA9D,cAncF,SAAqCjiB,GACnC,GAAIqS,EAAO8M,kBAAmB,CAC5B,IAAIoK,GAASlI,GAAe5S,IAAIzO,IAAQ,GAAK,EACzCupB,GAAS,GACXlI,GAAe1S,OAAO3O,GACtBkV,GAAgBhH,IAAIlO,IAEpBqhB,GAAejR,IAAIpQ,EAAKupB,EAE5B,MACEtH,GAAcjiB,GAEhByhB,GAAY,CAAEnK,SAAU,IAAIkJ,IAAIzgB,EAAMuX,WACxC,EAubEkS,QAxtEF,WACMhK,GACFA,IAEEuB,GACFA,IAEF/T,EAAYyc,QACZtJ,GAA+BA,EAA4BjR,QAC3DnP,EAAMuX,SAAS5P,SAAQ,CAACqC,EAAG/J,IAAQiiB,GAAcjiB,KACjDD,EAAM0gB,SAAS/Y,SAAQ,CAACqC,EAAG/J,IAAQomB,GAAcpmB,IACnD,EA8sEE0pB,WAtYF,SAAoB1pB,EAAagD,GAC/B,IAAIujB,EAAmBxmB,EAAM0gB,SAAShS,IAAIzO,IAAQwR,EAMlD,OAJI8P,GAAiB7S,IAAIzO,KAASgD,GAChCse,GAAiBlR,IAAIpQ,EAAKgD,GAGrBujB,CACT,EA+XEH,iBACAuD,YAxDF,SACEzS,EACApS,GAEA,IAAIoiB,EAAiC,MAAtBrI,EAEf1G,GACEjB,EACApS,EAHgB+Z,GAAsBG,EAKtCza,EACAF,GAQE6iB,IACFlI,EAAa,IAAIA,GACjByC,GAAY,CAAE,GAElB,EAkCEmI,0BAA2B3I,EAC3B4I,yBAA0BxO,GAG1ByO,mBAvEF,SAA4BC,GAC1BxlB,EAAW,CAAA,EACXsa,EAAqB1a,EACnB4lB,EACA1lB,OACAa,EACAX,EAEJ,GAkEOwa,CACT,wBA2BO,SACL3a,EACA+O,GAEAjU,EACEkF,EAAO2B,OAAS,EAChB,oEAGF,IAEI1B,EAFAE,EAA0B,CAAA,EAC1Bc,GAAY8N,EAAOA,EAAK9N,SAAW,OAAS,IAEhD,GAAQ,MAAJ8N,GAAAA,EAAM9O,mBACRA,EAAqB8O,EAAK9O,wBACrB,SAAI8O,GAAAA,EAAMyL,oBAAqB,CAEpC,IAAIA,EAAsBzL,EAAKyL,oBAC/Bva,EAAsBI,IAAW,CAC/BoN,iBAAkB+M,EAAoBna,IAE1C,MACEJ,EAAqBuN,EAGvB,IAAIS,EAAiChS,EAAA,CACnCqL,sBAAsB,EACtB4G,qBAAqB,GACjBa,EAAOA,EAAKd,OAAS,MAGvB2M,EAAa7a,EACfC,EACAC,OACAa,EACAX,GA+MF6K,eAAe4a,EACb7X,EACAvS,EACA4G,EACA0S,EACA+F,EACA3D,EACA2O,GAEA/qB,EACEiT,EAAQxE,OACR,wEAGF,IACE,GAAImG,GAAiB3B,EAAQK,OAAOhI,eAAgB,CAClD,IAAIjC,QA8CV6G,eACE+C,EACA3L,EACAsd,EACA5K,EACA+F,EACA3D,EACAlJ,GAEA,IAAI7J,EAEJ,GAAKub,EAAYrf,MAAMxC,QAAW6hB,EAAYrf,MAAMsR,KAa7C,CAULxN,SAToBwb,EAClB,SACA5R,EACA,CAAC2R,GACDtd,EACA4L,EACA8G,EACA+F,IAEe,GAEb9M,EAAQxE,OAAOe,SACjBwD,GAA+BC,EAASC,EAAgBC,EAE5D,KA5B0D,CACxD,IAAI5O,EAAQ8P,GAAuB,IAAK,CACtCf,OAAQL,EAAQK,OAChBlS,SAAU,IAAIuC,IAAIsP,EAAQ/O,KAAK9C,SAC/B4W,QAAS4M,EAAYrf,MAAMG,KAE7B,GAAIwN,EACF,MAAM3O,EAER8E,EAAS,CACPmL,KAAM1P,EAAWP,MACjBA,QAEJ,CAiBA,GAAIiY,GAAiBnT,GAKnB,MAAM,IAAI8H,SAAS,KAAM,CACvBJ,OAAQ1H,EAAOqS,SAAS3K,OACxBC,QAAS,CACPga,SAAU3hB,EAAOqS,SAAS1K,QAAQzB,IAAI,eAK5C,GAAIoN,GAAiBtT,GAAS,CAC5B,IAAI9E,EAAQ8P,GAAuB,IAAK,CAAEG,KAAM,iBAChD,GAAItB,EACF,MAAM3O,EAER8E,EAAS,CACPmL,KAAM1P,EAAWP,MACjBA,QAEJ,CAEA,GAAI2O,EAAgB,CAGlB,GAAIoD,GAAcjN,GAChB,MAAMA,EAAO9E,MAGf,MAAO,CACL+C,QAAS,CAACsd,GACVhd,WAAY,CAAE,EACdyV,WAAY,CAAE,CAACuH,EAAYrf,MAAMG,IAAK2D,EAAOvB,MAC7CkP,OAAQ,KAGRN,WAAY,IACZ4F,cAAe,CAAE,EACjB2O,cAAe,CAAE,EACjB9O,gBAAiB,KAErB,CAGA,IAAI+O,EAAgB,IAAIjP,QAAQhJ,EAAQ/O,IAAK,CAC3C8M,QAASiC,EAAQjC,QACjBF,SAAUmC,EAAQnC,SAClBrC,OAAQwE,EAAQxE,SAGlB,GAAI6H,GAAcjN,GAAS,CAGzB,IAAIoT,EAAgBL,EAChBwI,EACAlI,GAAoBpV,EAASsd,EAAYrf,MAAMG,IAanD,OAAAvE,WAXoBgqB,EAClBD,EACA5jB,EACA0S,EACA+F,EACA3D,EACA,KACA,CAACK,EAAclX,MAAMG,GAAI2D,IAKf,CACVqN,WAAYnF,EAAqBlI,EAAO9E,OACpC8E,EAAO9E,MAAMwM,OACQ,MAArB1H,EAAOqN,WACPrN,EAAOqN,WACP,IACJ2G,WAAY,KACZ4N,cAAa9pB,EAAA,GACPkI,EAAO2H,QAAU,CAAE,CAAC4T,EAAYrf,MAAMG,IAAK2D,EAAO2H,SAAY,KAGxE,CAWA,OAAA7P,WAToBgqB,EAClBD,EACA5jB,EACA0S,EACA+F,EACA3D,EACA,MAIU,CACViB,WAAY,CACV,CAACuH,EAAYrf,MAAMG,IAAK2D,EAAOvB,OAG7BuB,EAAOqN,WAAa,CAAEA,WAAYrN,EAAOqN,YAAe,GAAE,CAC9DuU,cAAe5hB,EAAO2H,QAClB,CAAE,CAAC4T,EAAYrf,MAAMG,IAAK2D,EAAO2H,SACjC,CAAC,GAET,CA/LyBoa,CACjBnY,EACA3L,EACAyjB,GAAczS,GAAehR,EAAS5G,GACtCsZ,EACA+F,EACA3D,EACc,MAAd2O,GAEF,OAAO1hB,CACT,CAEA,IAAIA,QAAe8hB,EACjBlY,EACA3L,EACA0S,EACA+F,EACA3D,EACA2O,GAEF,OAAO7P,GAAW7R,GACdA,EAAMlI,EAAA,CAAA,EAEDkI,EAAM,CACTgU,WAAY,KACZ4N,cAAe,CAAC,GAkBxB,CAhBE,MAAOzqB,GAIP,GAstDN,SAAyB6I,GACvB,OACY,MAAVA,GACkB,iBAAXA,GACP,SAAUA,GACV,WAAYA,IACXA,EAAOmL,OAAS1P,EAAWgD,MAAQuB,EAAOmL,OAAS1P,EAAWP,MAEnE,CA9tDU8mB,CAAgB7qB,IAAM0a,GAAW1a,EAAE6I,QAAS,CAC9C,GAAI7I,EAAEgU,OAAS1P,EAAWP,MACxB,MAAM/D,EAAE6I,OAEV,OAAO7I,EAAE6I,MACX,CAGA,GAgwDN,SAA4BA,GAC1B,IAAK6R,GAAW7R,GACd,OAAO,EAGT,IAAI0H,EAAS1H,EAAO0H,OAChBrQ,EAAW2I,EAAO2H,QAAQzB,IAAI,YAClC,OAAOwB,GAAU,KAAOA,GAAU,KAAmB,MAAZrQ,CAC3C,CAxwDU4qB,CAAmB9qB,GACrB,OAAOA,EAET,MAAMA,CACR,CACF,CAqJA0P,eAAeib,EACblY,EACA3L,EACA0S,EACA+F,EACA3D,EACA2O,EACA3U,GAQA,IAAIlD,EAA+B,MAAd6X,EAGrB,GACE7X,IACC6X,MAAAA,IAAAA,EAAYxlB,MAAMuR,UAClBiU,MAAAA,IAAAA,EAAYxlB,MAAMsR,MAEnB,MAAMxC,GAAuB,IAAK,CAChCf,OAAQL,EAAQK,OAChBlS,SAAU,IAAIuC,IAAIsP,EAAQ/O,KAAK9C,SAC/B4W,QAAmB,MAAV+S,OAAU,EAAVA,EAAYxlB,MAAMG,KAI/B,IAKIqU,GALiBgR,EACjB,CAACA,GACD3U,GAAuBE,GAAcF,EAAoB,IACzDf,GAA8B/N,EAAS8O,EAAoB,IAC3D9O,GAC+BsC,QAChC6L,GAAMA,EAAElQ,MAAMuR,QAAUrB,EAAElQ,MAAMsR,OAInC,GAA6B,IAAzBkD,EAAclT,OAChB,MAAO,CACLS,UAEAM,WAAYN,EAAQuC,QAClB,CAAC8E,EAAK8G,IAAMnL,OAAO5F,OAAOiK,EAAK,CAAE,CAAC8G,EAAElQ,MAAMG,IAAK,QAC/C,CAAA,GAEFsR,OACEZ,GAAuBE,GAAcF,EAAoB,IACrD,CACE,CAACA,EAAoB,IAAKA,EAAoB,GAAG7R,OAEnD,KACNmS,WAAY,IACZ4F,cAAe,CAAE,EACjBH,gBAAiB,MAIrB,IAAIhC,QAAgB0K,EAClB,SACA5R,EACA8G,EACAzS,EACA4L,EACA8G,EACA+F,GAGE9M,EAAQxE,OAAOe,SACjBwD,GAA+BC,EAASC,EAAgBC,GAI1D,IAAIgJ,EAAkB,IAAImF,IACtBzG,EAAUqB,GACZ5U,EACAyS,EACAI,EACA/D,EACA+F,EACAC,GAIEmP,EAAkB,IAAIvmB,IACxB+U,EAAczU,KAAKqC,GAAUA,EAAMpC,MAAMG,MAQ3C,OANA4B,EAAQkB,SAASb,IACV4jB,EAAgBtT,IAAItQ,EAAMpC,MAAMG,MACnCmV,EAAQjT,WAAWD,EAAMpC,MAAMG,IAAM,KACvC,IAGFvE,KACK0Z,EAAO,CACVvT,UACA6U,gBACEA,EAAgB/L,KAAO,EACnB9F,OAAOkhB,YAAYrP,EAAgBzN,WACnC,MAEV,CAIAwB,eAAe2U,EACbrQ,EACAvB,EACA8G,EACAzS,EACA4L,EACA8G,EACA+F,GAEA,IAAI5F,QAAgBN,GAClBkG,GAAyBpG,GACzBnF,EACAvB,EACA8G,EACAzS,EACAjC,EACAF,EACA6U,GAGF,aAAa7L,QAAQyL,IACnBO,EAAQ7U,KAAI,CAAC+D,EAAQrC,KACnB,GAAI4W,GAAwBvU,GAAS,CAGnC,MAAMoS,GAFSpS,EAAOA,OAIpB4J,EACA8G,EAAc/S,GAAGzB,MAAMG,GACvB4B,EACAnB,EACAgN,EAAO3G,qBAEX,CACA,GAAI0O,GAAW7R,EAAOA,SAAW6J,EAG/B,MAAM7J,EAGR,OAAO2R,GAAiC3R,EAAO,IAGrD,CAEA,MAAO,CACLyW,aACA2L,MA7hBFvb,eACE+C,EAAgByY,GAU0B,IAT1C1R,eACEA,EAAcoC,wBACdA,EAAuB2D,sBACvBA,QAKD,IAAA2L,EAAG,CAAA,EAAEA,EAEFxnB,EAAM,IAAIP,IAAIsP,EAAQ/O,KACtBoP,EAASL,EAAQK,OACjB5S,EAAWM,EAAe,GAAIY,EAAWsC,GAAM,KAAM,WACrDoD,EAAUrB,EAAY6Z,EAAYpf,EAAUyF,GAGhD,IAAKiO,GAAcd,IAAsB,SAAXA,EAAmB,CAC/C,IAAI/O,EAAQ8P,GAAuB,IAAK,CAAEf,YACpChM,QAASqkB,EAAuBpmB,MAAEA,GACtCiY,GAAuBsC,GACzB,MAAO,CACL3Z,WACAzF,WACA4G,QAASqkB,EACT/jB,WAAY,CAAE,EACdyV,WAAY,KACZrG,OAAQ,CACN,CAACzR,EAAMG,IAAKnB,GAEdmS,WAAYnS,EAAMwM,OAClBuL,cAAe,CAAE,EACjB2O,cAAe,CAAE,EACjB9O,gBAAiB,KAErB,CAAO,IAAK7U,EAAS,CACnB,IAAI/C,EAAQ8P,GAAuB,IAAK,CAAEjT,SAAUV,EAASU,YACvDkG,QAAS6c,EAAe5e,MAAEA,GAC9BiY,GAAuBsC,GACzB,MAAO,CACL3Z,WACAzF,WACA4G,QAAS6c,EACTvc,WAAY,CAAE,EACdyV,WAAY,KACZrG,OAAQ,CACN,CAACzR,EAAMG,IAAKnB,GAEdmS,WAAYnS,EAAMwM,OAClBuL,cAAe,CAAE,EACjB2O,cAAe,CAAE,EACjB9O,gBAAiB,KAErB,CAEA,IAAI9S,QAAeyhB,EACjB7X,EACAvS,EACA4G,EACA0S,EACA+F,GAAyB,MACG,IAA5B3D,EACA,MAEF,OAAIlB,GAAW7R,GACNA,EAMTlI,EAAA,CAAST,WAAUyF,YAAakD,EAClC,EAqdEuiB,WAzbF1b,eACE+C,EAAgB4Y,GAUF,IATd7T,QACEA,EAAOgC,eACPA,EAAc+F,sBACdA,QAKD,IAAA8L,EAAG,CAAA,EAAEA,EAEF3nB,EAAM,IAAIP,IAAIsP,EAAQ/O,KACtBoP,EAASL,EAAQK,OACjB5S,EAAWM,EAAe,GAAIY,EAAWsC,GAAM,KAAM,WACrDoD,EAAUrB,EAAY6Z,EAAYpf,EAAUyF,GAGhD,IAAKiO,GAAcd,IAAsB,SAAXA,GAAgC,YAAXA,EACjD,MAAMe,GAAuB,IAAK,CAAEf,WAC/B,IAAKhM,EACV,MAAM+M,GAAuB,IAAK,CAAEjT,SAAUV,EAASU,WAGzD,IAAIuG,EAAQqQ,EACR1Q,EAAQiW,MAAM9H,GAAMA,EAAElQ,MAAMG,KAAOsS,IACnCM,GAAehR,EAAS5G,GAE5B,GAAIsX,IAAYrQ,EACd,MAAM0M,GAAuB,IAAK,CAChCjT,SAAUV,EAASU,SACnB4W,YAEG,IAAKrQ,EAEV,MAAM0M,GAAuB,IAAK,CAAEjT,SAAUV,EAASU,WAGzD,IAAIiI,QAAeyhB,EACjB7X,EACAvS,EACA4G,EACA0S,EACA+F,GAAyB,MACzB,EACApY,GAGF,GAAIuT,GAAW7R,GACb,OAAOA,EAGT,IAAI9E,EAAQ8E,EAAO2N,OAAS1M,OAAOwhB,OAAOziB,EAAO2N,QAAQ,QAAKhR,EAC9D,QAAcA,IAAVzB,EAKF,MAAMA,EAIR,GAAI8E,EAAOgU,WACT,OAAO/S,OAAOwhB,OAAOziB,EAAOgU,YAAY,GAG1C,GAAIhU,EAAOzB,WAAY,CAAA,IAAAmkB,EACrB,IAAIjkB,EAAOwC,OAAOwhB,OAAOziB,EAAOzB,YAAY,GAI5C,OAHImkB,OAAJA,EAAI1iB,EAAO8S,kBAAP4P,EAAyBpkB,EAAMpC,MAAMG,MACvCoC,EAAKgL,GAA0BzJ,EAAO8S,gBAAgBxU,EAAMpC,MAAMG,KAE7DoC,CACT,CAGF,EAgXF,UDr4EoC,SAACA,EAAMiH,GAGzC,YAH6C,IAAJA,IAAAA,EAAO,CAAA,GAGzC,IAAIvB,EAAa1F,EAFW,iBAATiH,EAAoB,CAAEgC,OAAQhC,GAASA,EAGnE,iBAjtBO,SACLid,EACAnkB,QAEC,IAFDA,IAAAA,EAEI,CAAA,GAEJ,IAAI9F,EAAeiqB,EACfjqB,EAAKmH,SAAS,MAAiB,MAATnH,IAAiBA,EAAKmH,SAAS,QACvD9I,GACE,EACA,eAAe2B,EAAf,oCACMA,EAAK2B,QAAQ,MAAO,MAD1B,qIAGsC3B,EAAK2B,QAAQ,MAAO,MAAK,MAEjE3B,EAAOA,EAAK2B,QAAQ,MAAO,OAI7B,MAAMuoB,EAASlqB,EAAKqG,WAAW,KAAO,IAAM,GAEtCiE,EAAa6f,GACZ,MAALA,EAAY,GAAkB,iBAANA,EAAiBA,EAAIzmB,OAAOymB,GA4BtD,OAAOD,EA1BUlqB,EACd+G,MAAM,OACNxD,KAAI,CAACwE,EAASnJ,EAAOwrB,KAIpB,GAHsBxrB,IAAUwrB,EAAMtlB,OAAS,GAGd,MAAZiD,EAAiB,CAGpC,OAAOuC,EAAUxE,EAFJ,KAGf,CAEA,MAAMukB,EAAWtiB,EAAQnC,MAAM,oBAC/B,GAAIykB,EAAU,CACZ,OAAStrB,EAAKurB,GAAYD,EAC1B,IAAIE,EAAQzkB,EAAO/G,GAEnB,OADAd,EAAuB,MAAbqsB,GAA6B,MAATC,EAAa,aAAexrB,EAAG,WACtDuL,EAAUigB,EACnB,CAGA,OAAOxiB,EAAQpG,QAAQ,OAAQ,GAAG,IAGnCkG,QAAQE,KAAcA,IAEAnE,KAAK,IAChC,8BC2iGO,SACLT,EACA2V,EACAtW,GASA,OAPoCpD,EAAA,CAAA,EAC/B0Z,EAAO,CACVnE,WAAYnF,EAAqBhN,GAASA,EAAMwM,OAAS,IACzDiG,OAAQ,CACN,CAAC6D,EAAQ0R,4BAA8BrnB,EAAO,GAAGQ,IAAKnB,IAI5D,kBDnqFO,SAAuBrD,GAE5B,MAAc,KAAPA,GAAuC,KAAzBA,EAAYE,SAC7B,IACc,iBAAPF,EACPK,EAAUL,GAAIE,SACdF,EAAGE,QACT,oEAuCkC,SAAC0G,EAAMiH,QAAI,IAAJA,IAAAA,EAAO,CAAA,GAC9C,IAAIrB,EAA+B,iBAATqB,EAAoB,CAAEgC,OAAQhC,GAASA,EAE7DiC,EAAU,IAAIC,QAAQvD,EAAasD,SAKvC,OAJKA,EAAQiH,IAAI,iBACfjH,EAAQE,IAAI,eAAgB,mCAGvB,IAAIC,SAAS/E,KAAKC,UAAUvE,GAAK3G,EAAA,CAAA,EACnCuM,EAAY,CACfsD,YAEJ,oGAgPkDwb,CAACtoB,EAAK6K,KACtD,IAAI2M,EAAW5K,EAAS5M,EAAK6K,GAE7B,OADA2M,EAAS1K,QAAQE,IAAI,0BAA2B,QACzCwK,CAAQ"}