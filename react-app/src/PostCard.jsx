// src/components/PostCard.jsx
import React from 'react';
import { useNavigate } from 'react-router-dom';

export default function PostCard({ post, user, showActions = false }) {
  const navigate = useNavigate();

  const handleDelete = async () => {
    const confirmed = window.confirm("Are you sure you want to delete this post?");
    if (!confirmed) return;

    try {
      const res = await fetch(`/api/posts/${post.id}`, {
        method: "DELETE",
        credentials: "include"
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({ detail: "Failed to delete post" }));
        throw new Error(data.detail || "Something went wrong");
      }

      alert("Post deleted successfully!");
      // Trigger global post refresh
      localStorage.setItem('postsUpdated', Date.now().toString());
      window.location.reload(); // Simple refresh for now
    } catch (err) {
      alert(err.message);
    }
  };

  const handleEdit = () => {
    navigate(`/posts/${post.id}/edit`);
  };
  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <h4 className="text-xl font-bold mb-2">{post.title}</h4>
      <p className="text-gray-700 mb-3 line-clamp-3">{post.content}</p>
      {post.image_url && (
        <img 
          src={post.image_url} 
          alt={post.title}
          className="w-full h-40 object-cover rounded mb-4"
        />
      )}
      <small className="block text-sm text-gray-500 mb-3">
        By {post.author_username} • {new Date(post.created_at).toLocaleDateString()}
      </small>

      <div className="flex gap-2 items-center">
        <a href={`/posts/${post.id}`} className="text-indigo-600 hover:underline">
          View Post
        </a>

        {/* Show edit/delete buttons if user owns the post or is admin */}
        {showActions && user && (user.username === post.author_username || user.role === 'admin') && (
          <>
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:underline text-sm"
            >
              🖋️ Edit
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:underline text-sm"
            >
              🗑️ Delete
            </button>
          </>
        )}
      </div>
    </div>
  );
}