import React, { useState, useEffect } from 'react';

export default function CommentPreview({ postId, maxComments = 2 }) {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalComments, setTotalComments] = useState(0);

  useEffect(() => {
    fetchComments();
  }, [postId]);

  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/comments/${postId}`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const data = await response.json();
        setComments(data.slice(0, maxComments)); // Show only first few comments
        setTotalComments(data.length);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-500">Loading comments...</p>
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-500 italic">No comments yet. Be the first to comment!</p>
      </div>
    );
  }

  return (
    <div className="mt-4 space-y-3">
      {comments.map((comment, index) => (
        <div key={comment.id || index} className="bg-gray-50 p-3 rounded-lg">
          <div className="flex items-start space-x-2">
            <div className="w-6 h-6 bg-indigo-400 rounded-full flex items-center justify-center text-white text-xs font-semibold">
              {comment.author?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">
                  {comment.author || 'Anonymous'}
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(comment.created_at).toLocaleDateString()}
                </span>
              </div>
              <p className="text-sm text-gray-700 mt-1 break-words">
                {comment.content}
              </p>
            </div>
          </div>
        </div>
      ))}
      
      {totalComments > maxComments && (
        <div className="text-center">
          <a 
            href={`/posts/${postId}#comments`}
            className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
          >
            View all {totalComments} comments →
          </a>
        </div>
      )}
    </div>
  );
}
