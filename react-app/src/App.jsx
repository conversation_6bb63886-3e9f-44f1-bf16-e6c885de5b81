import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { AnnouncerProvider } from './Announcer';
import Layout from './Layout.jsx';
import Index from './Index.jsx';
import Dashboard from './Dashboard.jsx';
import Login from './Login.jsx';
import SignUp from './SignUp.jsx';
import RegisterMentor from './RegisterMentor.jsx';
import MentorProfile from './MentorProfile.jsx';
import Chat from './Chat.jsx';
import DirectMessage from './DirectMessage.jsx';
import Mentors from './Mentors.jsx';
import PostDetail from './PostDetail.jsx';
import PostList from './PostList.jsx'; // For search/sort
import ActivityFeed from './ActivityFeed.jsx'; // For user activity
import EditPostForm from './EditPostForm.jsx';
import EditPost from './EditPost.jsx';
import CreateProfile from './CreateProfile.jsx';
import Profile from './Profile.jsx';
import ArchivedChats from './ArchivedChats.jsx';
import DownloadApp from './DownloadApp.jsx';
import CommentSection from './CommentSection';




export default function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch current user info
  useEffect(() => {
    fetch('/api/user', {
      credentials: 'include'
    })
    .then(res => {
      if (res.ok) return res.json();
      throw new Error('Not authenticated');
    })
    .then(data => setUser(data))
    .catch(() => setUser(null))
    .finally(() => setLoading(false));
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <p>Loading...</p>
      </div>
    );
  }

  return ( 
    <AnnouncerProvider>
    <Routes>
      <Route path="/" element={<Layout user={user}><Index user={user} /></Layout>} />
      <Route path="/dashboard" element={<Layout user={user}><Dashboard user={user} /></Layout>} />
      <Route path="/login" element={<Layout user={user}><Login onLogin={setUser} /></Layout>} />
      <Route path="/signup" element={<Layout user={user}><SignUp onLogin={setUser} /></Layout>} />
      <Route path="/posts/:id" element={<Layout user={user}><PostDetail user={user} /></Layout>} />
      <Route path="/posts/:postId/edit" element={<Layout user={user}><EditPost user={user} /></Layout>} />
      <Route path="/register-mentor" element={<Layout user={user}><RegisterMentor user={user} /></Layout>} />
      <Route path="/mentors/:mentorId" element={<Layout user={user}><MentorProfile user={user} /></Layout>} />
      <Route path='/mentors' element={<Layout user={user}><Mentors user={user} /></Layout>} />
      <Route path="/chat" element={<Layout user={user}><Chat user={user} /></Layout>} />
      <Route path="/dm/:username" element={<Layout user={user}><DirectMessage user={user} /></Layout>} />
      <Route path="/profile" element={<Layout user={user}><CreateProfile user={user} /></Layout>} />
      <Route path="/activity" element={<Layout user={user}><ActivityFeed user={user} /></Layout>} />
      <Route path="/posts" elements={<Layout user={user}><PostList user={user} /></Layout>} />
      <Route path="/editpostform" element={<Layout user={user}><EditPostForm /></Layout>} />
      <Route path="/profile" element={<Layout><Profile user={user} /></Layout>} />
      <Route path="/archived-chats" element={<Layout><ArchivedChats user={user} /></Layout>} />
      <Route path="/download" element={<Layout><DownloadApp user={user} /></Layout>} />
      <Route path="/comments" element={<Layout><CommentSection user={user} /></Layout>} />
      
 
    </Routes>
    </AnnouncerProvider>
  );
}
