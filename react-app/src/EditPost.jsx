import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import 'katex/dist/katex.min.css';

export default function EditPost({ user }) {
  const { postId } = useParams(); // Get ID from route
  const [post, setPost] = useState(null);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [image, setImage] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const quillRef = useRef(null);

  // Custom image upload handler
  const imageHandler = () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = async () => {
      const file = input.files[0];
      if (file) {
        const formData = new FormData();
        formData.append('image', file);

        try {
          const response = await fetch('/api/upload-image', {
            method: 'POST',
            body: formData,
          });

          if (response.ok) {
            const data = await response.json();
            const imageUrl = data.url;

            const quill = quillRef.current.getEditor();
            const range = quill.getSelection();
            quill.insertEmbed(range.index, 'image', imageUrl);
          } else {
            const reader = new FileReader();
            reader.onload = (e) => {
              const quill = quillRef.current.getEditor();
              const range = quill.getSelection();
              quill.insertEmbed(range.index, 'image', e.target.result);
            };
            reader.readAsDataURL(file);
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          const reader = new FileReader();
          reader.onload = (e) => {
            const quill = quillRef.current.getEditor();
            const range = quill.getSelection();
            quill.insertEmbed(range.index, 'image', e.target.result);
          };
          reader.readAsDataURL(file);
        }
      }
    };
  };

  // Quill editor configuration
  const quillModules = useMemo(() => ({
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }, { 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }, { 'align': [] }],
        ['link', 'image', 'video', 'formula'],
        ['blockquote', 'code-block'],
        ['clean']
      ],
      handlers: {
        image: imageHandler
      }
    },
    clipboard: {
      matchVisual: false,
    },
    history: {
      delay: 2000,
      maxStack: 500,
      userOnly: true
    }
  }), []);

  const quillFormats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'script',
    'list', 'bullet', 'check', 'indent',
    'direction', 'align',
    'link', 'image', 'video', 'formula',
    'blockquote', 'code-block',
    'clean'
  ];

  // Fetch the existing post
  useEffect(() => {
    async function fetchPost() {
      const res = await fetch(`/api/posts/${postId}`, {
        credentials: 'include'
      });

      if (!res.ok) {
        navigate("/dashboard");
        return;
      }

      const data = await res.json();
      if (data.author_username !== user?.username && user?.role !== "admin") {
        navigate("/dashboard");
        return;
      }

      setTitle(data.title);
      setContent(data.content);
      setPost(data);
      setLoading(false);
    }

    if (user) {
      fetchPost();
    }
  }, [postId, user, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("title", title);
    formData.append("content", content);
    if (image) {
      formData.append("post_image", image);
    }

    try {
      const res = await fetch(`/api/posts/${postId}`, {
        method: "POST",
        body: formData,
        credentials: 'include'
      });

      if (res.ok) {
        alert("Post updated successfully!");
        // Trigger global post refresh
        localStorage.setItem('postsUpdated', Date.now().toString());
        navigate(`/posts/${postId}`);
      } else {
        const errorData = await res.json().catch(() => ({ detail: "Failed to update post" }));
        alert(`Failed to update post: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (err) {
      console.error("Error updating post:", err);
      alert(`Network error – could not update post: ${err.message}`);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading post...</p>
      </div>
    );
  }

  return (
    <main className="container mx-auto py-10 px-4 max-w-3xl">
      <h2 className="text-2xl font-bold mb-6">Edit Your Post</h2>

      <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-6">
        {/* Title */}
        <div className="mb-4">
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">Title</label>
          <input
            id="title"
            type="text"
            name="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        {/* Content Rich Text Editor */}
        <div className="mb-6">
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
            Content
            <span className="text-xs text-gray-500 ml-2">
              ({content.replace(/<[^>]*>/g, '').length} characters)
            </span>
          </label>
          <div className="border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500">
            <ReactQuill
              ref={quillRef}
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Edit your post content here..."
              style={{
                minHeight: '300px',
                backgroundColor: 'white'
              }}
            />
          </div>
        </div>

        {/* Image Upload */}
        <div className="mb-6">
          <label htmlFor="post_image" className="block text-sm font-medium text-gray-700 mb-1">Update Image (Optional)</label>
          <input
            id="post_image"
            type="file"
            name="post_image"
            accept="image/*"
            onChange={(e) => setImage(e.target.files[0])}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md transition"
        >
          Update Post
        </button>
      </form>
    </main>
  );
}