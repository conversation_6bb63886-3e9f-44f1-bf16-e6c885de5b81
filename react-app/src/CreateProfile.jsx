import React, { useState, useEffect } from 'react';

export default function CreateProfile() {
  const [fullName, setFullName] = useState("");
  const [bio, setBio] = useState("");
  const [github, setGithub] = useState("");
  const [avatarUrl, setAvatarUrl] = useState("/static/avatars/default.png");
  const [loading, setLoading] = useState(false);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => setAvatarUrl(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const formData = new FormData();
    formData.append("full_name", fullName);
    formData.append("bio", bio);
    formData.append("github_username", github);
    if (e.target.avatar.files.length > 0) {
      formData.append("avatar", e.target.avatar.files[0]);
    }

    try {
      console.log("Submitting profile data:", { fullName, bio, github });
      const res = await fetch("/create-profile", {
        method: "POST",
        credentials: "include",
        body: formData
      });

      console.log("Response status:", res.status);

      if (res.ok) {
        console.log("Profile created successfully");
        alert("Profile saved successfully!");
        window.location.href = "/dashboard";
      } else {
        const errorText = await res.text();
        console.error("Profile creation failed:", errorText);
        alert(`Update failed: ${errorText}`);
      }
    } catch (err) {
      console.error("Profile creation error:", err);
      alert(`Error updating profile: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6">
      <h2 className="text-2xl font-bold">Complete Your Profile</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Full Name</label>
          <input
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            className="w-full px-4 py-2 border rounded"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Bio</label>
          <textarea
            rows="4"
            value={bio}
            onChange={(e) => setBio(e.target.value)}
            className="w-full px-4 py-2 border rounded"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">GitHub Username</label>
          <input
            type="text"
            value={github}
            onChange={(e) => setGithub(e.target.value)}
            className="w-full px-4 py-2 border rounded"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Avatar</label>
          <img src={avatarUrl} alt="Current Avatar" className="w-20 h-20 rounded-full object-cover mb-2" />
          <input type="file" name="avatar" onChange={handleImageChange} accept="image/*" />
        </div>

        <button
          type="submit"
          className="mt-4 w-full py-2 px-4 rounded bg-indigo-600 hover:bg-indigo-700 text-white"
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Profile"}
        </button>
      </form>
    </div>
  );
}