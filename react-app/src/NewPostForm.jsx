// NewPostForm.jsx
import React, { useState, useRef, useMemo } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import 'katex/dist/katex.min.css';

// Enhanced Quill editor with custom image handling

export default function NewPostForm() {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [image, setImage] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const quillRef = useRef(null);

  // Custom image upload handler
  const imageHandler = () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = async () => {
      const file = input.files[0];
      if (file) {
        // Create a FormData object to upload the image
        const formData = new FormData();
        formData.append('image', file);

        try {
          // Upload image to your backend
          const response = await fetch('/api/upload-image', {
            method: 'POST',
            body: formData,
          });

          if (response.ok) {
            const data = await response.json();
            const imageUrl = data.url; // Assuming your backend returns { url: "image_url" }

            // Insert the image into the editor
            const quill = quillRef.current.getEditor();
            const range = quill.getSelection();
            quill.insertEmbed(range.index, 'image', imageUrl);
          } else {
            // Fallback: convert to base64 for local preview
            const reader = new FileReader();
            reader.onload = (e) => {
              const quill = quillRef.current.getEditor();
              const range = quill.getSelection();
              quill.insertEmbed(range.index, 'image', e.target.result);
            };
            reader.readAsDataURL(file);
          }
        } catch (error) {
          console.error('Image upload failed:', error);
          // Fallback: convert to base64 for local preview
          const reader = new FileReader();
          reader.onload = (e) => {
            const quill = quillRef.current.getEditor();
            const range = quill.getSelection();
            quill.insertEmbed(range.index, 'image', e.target.result);
          };
          reader.readAsDataURL(file);
        }
      }
    };
  };

  // Quill editor configuration with enhanced features
  const quillModules = useMemo(() => ({
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }, { 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'list': 'check' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }, { 'align': [] }],
        ['link', 'image', 'video', 'formula'],
        ['blockquote', 'code-block'],
        ['clean']
      ],
      handlers: {
        image: imageHandler
      }
    },
    clipboard: {
      matchVisual: false,
    },
    history: {
      delay: 2000,
      maxStack: 500,
      userOnly: true
    }
  }), []);

  const quillFormats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'script',
    'list', 'bullet', 'check', 'indent',
    'direction', 'align',
    'link', 'image', 'video', 'formula',
    'blockquote', 'code-block',
    'clean'
  ];

  // Word count function
  const getWordCount = (text) => {
    const plainText = text.replace(/<[^>]*>/g, '').trim();
    return plainText ? plainText.split(/\s+/).length : 0;
  };

  // Reading time estimation (average 200 words per minute)
  const getReadingTime = (text) => {
    const wordCount = getWordCount(text);
    const minutes = Math.ceil(wordCount / 200);
    return minutes;
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => setPreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    // Validate content - check if it's not just empty HTML
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    if (!textContent) {
      setError("Please enter some content for your post");
      setLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append("title", title);
    formData.append("content", content);
    if (image) formData.append("image", image);

    try {
      const res = await fetch("/api/posts", {
        method: "POST",
        body: formData,
        credentials: "include"
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({
          detail: "Failed to create post"
        }));
        throw new Error(data.detail || "Something went wrong");
      }

      await res.json(); // Response processed successfully
      setSuccess("✅ Post published successfully!");
      setTitle("");
      setContent("");
      setImage(null);
      setPreview(null);

      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 1000);

    } catch (err) {
      setError(err.message || "❌ Error creating post");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded shadow-md mb-6">
      <h3 className="text-lg font-semibold mb-4">✍️ Create New Post</h3>

      {/* Custom styles for Enhanced Quill editor */}
      <style jsx>{`
        .ql-editor {
          min-height: 250px;
          font-size: 14px;
          line-height: 1.6;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          background: #f9fafb;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: italic;
        }
        .ql-toolbar .ql-formats {
          margin-right: 8px;
        }
        .ql-toolbar button {
          padding: 4px;
          margin: 1px;
        }
        .ql-toolbar button:hover {
          background: #e5e7eb;
          border-radius: 3px;
        }
        .ql-toolbar .ql-active {
          background: #dbeafe;
          color: #1d4ed8;
          border-radius: 3px;
        }
        .ql-editor img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ql-editor blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 16px;
          margin: 16px 0;
          font-style: italic;
          color: #6b7280;
        }
        .ql-editor pre.ql-syntax {
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          padding: 12px;
          overflow-x: auto;
        }
        .ql-editor .ql-video {
          width: 100%;
          height: 315px;
        }
        .ql-snow .ql-tooltip {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .ql-snow .ql-tooltip input[type=text] {
          border: 1px solid #d1d5db;
          border-radius: 4px;
          padding: 4px 8px;
        }
        .ql-snow .ql-picker-options {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}</style>

      {/* Show error message */}
      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Show success message */}
      {success && (
        <div className="bg-green-100 text-green-700 p-3 rounded mb-4">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Title Input */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium mb-1">Title</label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            disabled={loading}
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"
          />
        </div>

        {/* Content Rich Text Editor */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium mb-1">
            Content
            <span className="text-xs text-gray-500 ml-2">
              ({content.replace(/<[^>]*>/g, '').length} characters • {getWordCount(content)} words • ~{getReadingTime(content)} min read)
            </span>
          </label>
          <div className="border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500">
            <ReactQuill
              ref={quillRef}
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Write your post content here... Use the enhanced toolbar for rich formatting, images, and more!"
              readOnly={loading}
              style={{
                minHeight: '300px',
                backgroundColor: loading ? '#f3f4f6' : 'white'
              }}
            />
          </div>
          {content && (
            <div className="mt-2 space-y-1">
              <div className="text-xs text-gray-500">
                ✨ <strong>Enhanced Features:</strong> Images, videos, formulas, tables, colors, fonts, and more!
              </div>
              <div className="text-xs text-gray-400">
                📸 <strong>Images:</strong> Click the image icon to upload • 🎨 <strong>Colors:</strong> Highlight text and use color tools • 📐 <strong>Math:</strong> Use formula button for equations
              </div>
            </div>
          )}
        </div>

        {/* Image Upload */}
        <div>
          <label htmlFor="image" className="block text-sm font-medium mb-1">Image (optional)</label>
          <input
            id="image"
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            disabled={loading}
            className="w-full px-2 py-1 border border-gray-300 rounded"
          />
        </div>

        {/* Preview Image */}
        {preview && (
          <img
            src={preview}
            alt="Preview"
            className="mt-2 w-full h-48 object-cover rounded"
          />
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className={`w-full py-2 px-4 rounded text-white font-medium transition ${
            loading ? "bg-indigo-400 cursor-not-allowed" : "bg-indigo-600 hover:bg-indigo-700"
          }`}
        >
          {loading ? "Publishing..." : "Publish Post"}
        </button>
      </form>
    </div>
  );
}