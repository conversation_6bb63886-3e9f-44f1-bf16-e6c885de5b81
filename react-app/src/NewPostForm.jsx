// NewPostForm.jsx
import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

export default function NewPostForm() {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [image, setImage] = useState(null);
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Quill editor configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link', 'blockquote', 'code-block'],
      [{ 'align': [] }],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent', 'link', 'blockquote',
    'code-block', 'align'
  ];

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onloadend = () => setPreview(reader.result);
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    // Validate content - check if it's not just empty HTML
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    if (!textContent) {
      setError("Please enter some content for your post");
      setLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append("title", title);
    formData.append("content", content);
    if (image) formData.append("image", image);

    try {
      const res = await fetch("/api/posts", {
        method: "POST",
        body: formData,
        credentials: "include"
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({
          detail: "Failed to create post"
        }));
        throw new Error(data.detail || "Something went wrong");
      }

      await res.json(); // Response processed successfully
      setSuccess("✅ Post published successfully!");
      setTitle("");
      setContent("");
      setImage(null);
      setPreview(null);

      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 1000);

    } catch (err) {
      setError(err.message || "❌ Error creating post");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded shadow-md mb-6">
      <h3 className="text-lg font-semibold mb-4">✍️ Create New Post</h3>

      {/* Custom styles for Quill editor */}
      <style jsx>{`
        .ql-editor {
          min-height: 150px;
          font-size: 14px;
          line-height: 1.6;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: italic;
        }
      `}</style>

      {/* Show error message */}
      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Show success message */}
      {success && (
        <div className="bg-green-100 text-green-700 p-3 rounded mb-4">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Title Input */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium mb-1">Title</label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            disabled={loading}
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"
          />
        </div>

        {/* Content Rich Text Editor */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium mb-1">
            Content
            <span className="text-xs text-gray-500 ml-2">
              ({content.replace(/<[^>]*>/g, '').length} characters)
            </span>
          </label>
          <div className="border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500">
            <ReactQuill
              theme="snow"
              value={content}
              onChange={setContent}
              modules={quillModules}
              formats={quillFormats}
              placeholder="Write your post content here... Use the toolbar above for formatting!"
              readOnly={loading}
              style={{
                minHeight: '200px',
                backgroundColor: loading ? '#f3f4f6' : 'white'
              }}
            />
          </div>
          {content && (
            <div className="mt-2 text-xs text-gray-500">
              💡 Tip: Your content supports rich formatting including headers, lists, links, and more!
            </div>
          )}
        </div>

        {/* Image Upload */}
        <div>
          <label htmlFor="image" className="block text-sm font-medium mb-1">Image (optional)</label>
          <input
            id="image"
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            disabled={loading}
            className="w-full px-2 py-1 border border-gray-300 rounded"
          />
        </div>

        {/* Preview Image */}
        {preview && (
          <img
            src={preview}
            alt="Preview"
            className="mt-2 w-full h-48 object-cover rounded"
          />
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className={`w-full py-2 px-4 rounded text-white font-medium transition ${
            loading ? "bg-indigo-400 cursor-not-allowed" : "bg-indigo-600 hover:bg-indigo-700"
          }`}
        >
          {loading ? "Publishing..." : "Publish Post"}
        </button>
      </form>
    </div>
  );
}