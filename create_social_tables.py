#!/usr/bin/env python3
"""
Create social interaction tables for likes and saves
"""

import sqlite3
import os

def create_social_tables():
    """Create the post_likes and saved_posts tables"""
    
    # Connect to the database
    db_path = "realhonest.db"
    if not os.path.exists(db_path):
        print(f"Database {db_path} not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create post_likes table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS post_likes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                post_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE,
                UNIQUE(user_id, post_id)
            )
        """)
        
        # Create saved_posts table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS saved_posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                post_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (post_id) REFERENCES posts (id) ON DELETE CASCADE,
                UNIQUE(user_id, post_id)
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_post_likes_post_id ON post_likes(post_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_post_likes_user_id ON post_likes(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_saved_posts_post_id ON saved_posts(post_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_saved_posts_user_id ON saved_posts(user_id)")
        
        conn.commit()
        print("✅ Social interaction tables created successfully!")
        
        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('post_likes', 'saved_posts')")
        tables = cursor.fetchall()
        print(f"✅ Created tables: {[table[0] for table in tables]}")
        
    except sqlite3.Error as e:
        print(f"❌ Error creating tables: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    create_social_tables()
